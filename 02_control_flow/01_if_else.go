// 01_if_else.go - Go语言条件语句
// 这个文件演示了Go语言中if-else条件语句的各种用法

package main

import (
	"fmt"
	"math/rand"
	"time"
)

func main() {
	fmt.Println("=== Go语言条件语句示例 ===")
	
	// 1. 基本if语句
	fmt.Println("\n--- 基本if语句 ---")
	age := 18
	
	if age >= 18 {
		fmt.Printf("年龄%d岁，已成年\n", age) // 输出: 年龄18岁，已成年
	}
	
	// 2. if-else语句
	fmt.Println("\n--- if-else语句 ---")
	score := 85
	
	if score >= 60 {
		fmt.Printf("分数%d，考试通过\n", score) // 输出: 分数85，考试通过
	} else {
		fmt.Printf("分数%d，考试不通过\n", score)
	}
	
	// 3. if-else if-else语句
	fmt.Println("\n--- if-else if-else语句 ---")
	temperature := 25
	
	if temperature > 30 {
		fmt.Printf("温度%d°C，天气炎热\n", temperature)
	} else if temperature > 20 {
		fmt.Printf("温度%d°C，天气温暖\n", temperature) // 输出: 温度25°C，天气温暖
	} else if temperature > 10 {
		fmt.Printf("温度%d°C，天气凉爽\n", temperature)
	} else {
		fmt.Printf("温度%d°C，天气寒冷\n", temperature)
	}
	
	// 4. 带初始化语句的if
	fmt.Println("\n--- 带初始化语句的if ---")
	// 在if语句中声明变量，变量作用域仅限于if语句块
	if num := 42; num%2 == 0 {
		fmt.Printf("数字%d是偶数\n", num) // 输出: 数字42是偶数
	} else {
		fmt.Printf("数字%d是奇数\n", num)
	}
	// num在这里不可访问
	
	// 5. 复杂条件判断
	fmt.Println("\n--- 复杂条件判断 ---")
	username := "admin"
	password := "123456"
	isActive := true
	
	if username == "admin" && password == "123456" && isActive {
		fmt.Println("登录成功") // 输出: 登录成功
	} else if username != "admin" {
		fmt.Println("用户名错误")
	} else if password != "123456" {
		fmt.Println("密码错误")
	} else if !isActive {
		fmt.Println("账户未激活")
	}
	
	// 6. 嵌套if语句
	fmt.Println("\n--- 嵌套if语句 ---")
	studentAge := 20
	hasLicense := true
	
	if studentAge >= 18 {
		fmt.Println("年龄符合要求")
		if hasLicense {
			fmt.Println("可以开车") // 输出: 年龄符合要求 \n 可以开车
		} else {
			fmt.Println("需要先考驾照")
		}
	} else {
		fmt.Println("年龄不符合要求")
	}
	
	// 7. 条件语句中的函数调用
	fmt.Println("\n--- 条件语句中的函数调用 ---")
	if length := len("Hello, World!"); length > 10 {
		fmt.Printf("字符串长度%d，较长\n", length) // 输出: 字符串长度13，较长
	} else {
		fmt.Printf("字符串长度%d，较短\n", length)
	}
	
	// 8. 随机数条件判断
	fmt.Println("\n--- 随机数条件判断 ---")
	rand.Seed(time.Now().UnixNano()) // 设置随机种子
	randomNum := rand.Intn(100)      // 生成0-99的随机数
	
	fmt.Printf("随机数: %d\n", randomNum)
	if randomNum < 30 {
		fmt.Println("随机数较小")
	} else if randomNum < 70 {
		fmt.Println("随机数中等")
	} else {
		fmt.Println("随机数较大")
	}
	
	// 9. 字符串条件判断
	fmt.Println("\n--- 字符串条件判断 ---")
	name := "张三"
	
	if name == "" {
		fmt.Println("姓名为空")
	} else if len(name) < 2 {
		fmt.Println("姓名太短")
	} else if len(name) > 10 {
		fmt.Println("姓名太长")
	} else {
		fmt.Printf("姓名'%s'符合要求\n", name) // 输出: 姓名'张三'符合要求
	}
	
	// 10. 布尔值直接判断
	fmt.Println("\n--- 布尔值直接判断 ---")
	isOnline := true
	isVIP := false
	
	if isOnline {
		fmt.Println("用户在线") // 输出: 用户在线
	}
	
	if !isVIP {
		fmt.Println("用户不是VIP") // 输出: 用户不是VIP
	}
	
	// 11. 类型断言条件判断
	fmt.Println("\n--- 类型断言条件判断 ---")
	var value interface{} = 42
	
	if num, ok := value.(int); ok {
		fmt.Printf("值%d是整数类型\n", num) // 输出: 值42是整数类型
	} else {
		fmt.Println("值不是整数类型")
	}
	
	// 12. 实际应用示例：成绩等级判定
	fmt.Println("\n--- 实际应用：成绩等级判定 ---")
	examScore := 88
	var grade string
	
	if examScore >= 90 {
		grade = "A"
	} else if examScore >= 80 {
		grade = "B"
	} else if examScore >= 70 {
		grade = "C"
	} else if examScore >= 60 {
		grade = "D"
	} else {
		grade = "F"
	}
	
	fmt.Printf("考试分数: %d, 等级: %s\n", examScore, grade) // 输出: 考试分数: 88, 等级: B
	
	// 13. 实际应用示例：用户权限检查
	fmt.Println("\n--- 实际应用：用户权限检查 ---")
	userRole := "admin"
	action := "delete"
	
	if userRole == "admin" {
		fmt.Printf("管理员可以执行%s操作\n", action) // 输出: 管理员可以执行delete操作
	} else if userRole == "editor" && action != "delete" {
		fmt.Printf("编辑者可以执行%s操作\n", action)
	} else if userRole == "viewer" && action == "read" {
		fmt.Printf("查看者可以执行%s操作\n", action)
	} else {
		fmt.Printf("用户%s没有权限执行%s操作\n", userRole, action)
	}
}

/*
运行命令: go run 02_control_flow/01_if_else.go

Go语言if语句特点：

1. 条件表达式不需要括号
2. 左大括号{必须在if语句同一行
3. 支持在条件判断前执行初始化语句
4. 初始化语句中声明的变量作用域仅限于if语句块
5. 支持嵌套if语句
6. 条件表达式必须是布尔类型

语法格式：
if condition {
    // 代码块
} else if condition {
    // 代码块
} else {
    // 代码块
}

带初始化语句的if：
if statement; condition {
    // 代码块
}

最佳实践：
1. 避免过深的嵌套，考虑使用early return
2. 复杂条件可以提取为变量或函数
3. 利用短路求值优化性能
4. 保持代码的可读性
*/
