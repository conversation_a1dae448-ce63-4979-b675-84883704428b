// 02_loops.go - Go语言循环语句
// 这个文件演示了Go语言中for循环的各种用法

package main

import "fmt"

func main() {
	fmt.Println("=== Go语言循环语句示例 ===")
	
	// 1. 基本for循环（类似C语言的for循环）
	fmt.Println("\n--- 基本for循环 ---")
	fmt.Print("数字1到5: ")
	for i := 1; i <= 5; i++ {
		fmt.Printf("%d ", i) // 输出: 1 2 3 4 5 
	}
	fmt.Println()
	
	// 2. 省略初始化语句的for循环
	fmt.Println("\n--- 省略初始化语句的for循环 ---")
	j := 1
	fmt.Print("倒数: ")
	for ; j <= 3; j++ {
		fmt.Printf("%d ", 4-j) // 输出: 3 2 1 
	}
	fmt.Println()
	
	// 3. 省略后置语句的for循环
	fmt.Println("\n--- 省略后置语句的for循环 ---")
	k := 0
	fmt.Print("偶数: ")
	for k < 10 {
		if k%2 == 0 {
			fmt.Printf("%d ", k) // 输出: 0 2 4 6 8 
		}
		k++
	}
	fmt.Println()
	
	// 4. 无限循环（需要break跳出）
	fmt.Println("\n--- 无限循环示例 ---")
	count := 0
	fmt.Print("无限循环计数: ")
	for {
		if count >= 5 {
			break // 跳出循环
		}
		fmt.Printf("%d ", count) // 输出: 0 1 2 3 4 
		count++
	}
	fmt.Println()
	
	// 5. 使用break和continue
	fmt.Println("\n--- break和continue示例 ---")
	fmt.Print("跳过3，遇到8停止: ")
	for i := 1; i <= 10; i++ {
		if i == 3 {
			continue // 跳过当前迭代
		}
		if i == 8 {
			break // 跳出循环
		}
		fmt.Printf("%d ", i) // 输出: 1 2 4 5 6 7 
	}
	fmt.Println()
	
	// 6. 嵌套循环
	fmt.Println("\n--- 嵌套循环：乘法表 ---")
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			fmt.Printf("%d*%d=%d ", i, j, i*j)
		}
		fmt.Println()
	}
	// 输出:
	// 1*1=1 1*2=2 1*3=3 
	// 2*1=2 2*2=4 2*3=6 
	// 3*1=3 3*2=6 3*3=9 
	
	// 7. 遍历数组
	fmt.Println("\n--- 遍历数组 ---")
	numbers := [5]int{10, 20, 30, 40, 50}
	
	// 方式1：使用索引
	fmt.Print("使用索引遍历: ")
	for i := 0; i < len(numbers); i++ {
		fmt.Printf("%d ", numbers[i]) // 输出: 10 20 30 40 50 
	}
	fmt.Println()
	
	// 方式2：使用range（推荐）
	fmt.Print("使用range遍历: ")
	for index, value := range numbers {
		fmt.Printf("[%d]=%d ", index, value) // 输出: [0]=10 [1]=20 [2]=30 [3]=40 [4]=50 
	}
	fmt.Println()
	
	// 8. 遍历切片
	fmt.Println("\n--- 遍历切片 ---")
	fruits := []string{"苹果", "香蕉", "橙子", "葡萄"}
	
	fmt.Print("水果列表: ")
	for i, fruit := range fruits {
		fmt.Printf("%d:%s ", i+1, fruit) // 输出: 1:苹果 2:香蕉 3:橙子 4:葡萄 
	}
	fmt.Println()
	
	// 只要值，不要索引
	fmt.Print("只要水果名: ")
	for _, fruit := range fruits {
		fmt.Printf("%s ", fruit) // 输出: 苹果 香蕉 橙子 葡萄 
	}
	fmt.Println()
	
	// 9. 遍历字符串
	fmt.Println("\n--- 遍历字符串 ---")
	text := "Hello世界"
	
	// 按字节遍历
	fmt.Print("按字节遍历: ")
	for i := 0; i < len(text); i++ {
		fmt.Printf("%c", text[i]) // 输出: Hello世界 (可能显示乱码)
	}
	fmt.Println()
	
	// 按Unicode字符遍历（推荐）
	fmt.Print("按字符遍历: ")
	for _, char := range text {
		fmt.Printf("%c ", char) // 输出: H e l l o 世 界 
	}
	fmt.Println()
	
	// 10. 遍历映射（map）
	fmt.Println("\n--- 遍历映射 ---")
	scores := map[string]int{
		"张三": 85,
		"李四": 92,
		"王五": 78,
	}
	
	fmt.Println("学生成绩:")
	for name, score := range scores {
		fmt.Printf("%s: %d分\n", name, score)
	}
	// 输出顺序是随机的，因为map是无序的
	
	// 11. 标签和goto（不推荐，但需要了解）
	fmt.Println("\n--- 标签和break/continue ---")
	fmt.Println("外层循环控制:")
	
outer:
	for i := 1; i <= 3; i++ {
		for j := 1; j <= 3; j++ {
			if i == 2 && j == 2 {
				fmt.Printf("在(%d,%d)处跳出外层循环\n", i, j)
				break outer // 跳出外层循环
			}
			fmt.Printf("(%d,%d) ", i, j)
		}
		fmt.Println()
	}
	// 输出: (1,1) (1,2) (1,3) 
	//      (2,1) 在(2,2)处跳出外层循环
	
	// 12. 实际应用：查找元素
	fmt.Println("\n--- 实际应用：查找元素 ---")
	names := []string{"Alice", "Bob", "Charlie", "David"}
	target := "Charlie"
	found := false
	index := -1
	
	for i, name := range names {
		if name == target {
			found = true
			index = i
			break
		}
	}
	
	if found {
		fmt.Printf("找到%s，位置: %d\n", target, index) // 输出: 找到Charlie，位置: 2
	} else {
		fmt.Printf("未找到%s\n", target)
	}
	
	// 13. 实际应用：计算总和和平均值
	fmt.Println("\n--- 实际应用：统计计算 ---")
	grades := []int{85, 92, 78, 96, 88}
	sum := 0
	
	for _, grade := range grades {
		sum += grade
	}
	
	average := float64(sum) / float64(len(grades))
	fmt.Printf("成绩: %v\n", grades)                    // 输出: 成绩: [85 92 78 96 88]
	fmt.Printf("总分: %d, 平均分: %.2f\n", sum, average) // 输出: 总分: 439, 平均分: 87.80
	
	// 14. 实际应用：生成图案
	fmt.Println("\n--- 实际应用：生成星号图案 ---")
	for i := 1; i <= 5; i++ {
		for j := 1; j <= i; j++ {
			fmt.Print("* ")
		}
		fmt.Println()
	}
	// 输出:
	// * 
	// * * 
	// * * * 
	// * * * * 
	// * * * * * 
}

/*
运行命令: go run 02_control_flow/02_loops.go

Go语言循环特点：

1. 只有for循环，没有while和do-while
2. for循环有多种形式：
   - for init; condition; post { }  // 标准形式
   - for condition { }              // 类似while
   - for { }                        // 无限循环
   - for range                      // 遍历集合

3. range关键字用法：
   - 数组/切片：for i, v := range arr
   - 字符串：for i, r := range str (r是rune类型)
   - 映射：for k, v := range map
   - 通道：for v := range ch

4. 控制语句：
   - break：跳出循环
   - continue：跳过当前迭代
   - 标签：可以跳出多层循环

5. 最佳实践：
   - 优先使用range遍历集合
   - 使用_忽略不需要的变量
   - 避免在循环中修改被遍历的集合
   - 合理使用break和continue提高代码可读性
*/
