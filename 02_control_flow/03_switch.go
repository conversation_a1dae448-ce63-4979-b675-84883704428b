// 03_switch.go - Go语言switch语句
// 这个文件演示了Go语言中switch语句的各种用法

package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("=== Go语言switch语句示例 ===")
	
	// 1. 基本switch语句
	fmt.Println("\n--- 基本switch语句 ---")
	day := 3
	
	switch day {
	case 1:
		fmt.Println("星期一") 
	case 2:
		fmt.Println("星期二")
	case 3:
		fmt.Println("星期三") // 输出: 星期三
	case 4:
		fmt.Println("星期四")
	case 5:
		fmt.Println("星期五")
	default:
		fmt.Println("周末")
	}
	
	// 2. 多个值的case
	fmt.Println("\n--- 多个值的case ---")
	month := 12
	
	switch month {
	case 12, 1, 2:
		fmt.Printf("%d月是冬季\n", month) // 输出: 12月是冬季
	case 3, 4, 5:
		fmt.Printf("%d月是春季\n", month)
	case 6, 7, 8:
		fmt.Printf("%d月是夏季\n", month)
	case 9, 10, 11:
		fmt.Printf("%d月是秋季\n", month)
	default:
		fmt.Println("无效的月份")
	}
	
	// 3. 带表达式的case
	fmt.Println("\n--- 带表达式的case ---")
	score := 85
	
	switch {
	case score >= 90:
		fmt.Printf("分数%d，等级A\n", score)
	case score >= 80:
		fmt.Printf("分数%d，等级B\n", score) // 输出: 分数85，等级B
	case score >= 70:
		fmt.Printf("分数%d，等级C\n", score)
	case score >= 60:
		fmt.Printf("分数%d，等级D\n", score)
	default:
		fmt.Printf("分数%d，等级F\n", score)
	}
	
	// 4. 带初始化语句的switch
	fmt.Println("\n--- 带初始化语句的switch ---")
	switch age := 25; {
	case age < 18:
		fmt.Printf("年龄%d，未成年\n", age)
	case age < 60:
		fmt.Printf("年龄%d，成年人\n", age) // 输出: 年龄25，成年人
	default:
		fmt.Printf("年龄%d，老年人\n", age)
	}
	
	// 5. fallthrough关键字
	fmt.Println("\n--- fallthrough关键字 ---")
	grade := 'B'
	
	fmt.Printf("等级%c的评价: ", grade)
	switch grade {
	case 'A':
		fmt.Print("优秀 ")
		fallthrough
	case 'B':
		fmt.Print("良好 ") // 输出: 良好 
		fallthrough
	case 'C':
		fmt.Print("及格 ") // 输出: 及格 (因为fallthrough)
		fallthrough
	default:
		fmt.Println("需要努力") // 输出: 需要努力 (因为fallthrough)
	}
	
	// 6. 字符串switch
	fmt.Println("\n--- 字符串switch ---")
	language := "Go"
	
	switch language {
	case "Go":
		fmt.Printf("%s是Google开发的语言\n", language) // 输出: Go是Google开发的语言
	case "Python":
		fmt.Printf("%s是一种解释型语言\n", language)
	case "Java":
		fmt.Printf("%s是面向对象语言\n", language)
	case "C", "C++":
		fmt.Printf("%s是系统编程语言\n", language)
	default:
		fmt.Printf("不了解%s语言\n", language)
	}
	
	// 7. 类型switch
	fmt.Println("\n--- 类型switch ---")
	var value interface{} = 42
	
	switch v := value.(type) {
	case int:
		fmt.Printf("整数类型，值为: %d\n", v) // 输出: 整数类型，值为: 42
	case string:
		fmt.Printf("字符串类型，值为: %s\n", v)
	case bool:
		fmt.Printf("布尔类型，值为: %t\n", v)
	case float64:
		fmt.Printf("浮点类型，值为: %.2f\n", v)
	default:
		fmt.Printf("未知类型: %T\n", v)
	}
	
	// 8. 时间相关的switch
	fmt.Println("\n--- 时间相关的switch ---")
	now := time.Now()
	
	switch now.Weekday() {
	case time.Monday:
		fmt.Println("今天是星期一，新的一周开始")
	case time.Tuesday, time.Wednesday, time.Thursday:
		fmt.Println("今天是工作日")
	case time.Friday:
		fmt.Println("今天是星期五，快到周末了")
	case time.Saturday, time.Sunday:
		fmt.Println("今天是周末，好好休息")
	}
	
	// 9. 实际应用：HTTP状态码处理
	fmt.Println("\n--- 实际应用：HTTP状态码处理 ---")
	statusCode := 404
	
	switch statusCode {
	case 200:
		fmt.Println("请求成功")
	case 301, 302:
		fmt.Println("重定向")
	case 400:
		fmt.Println("客户端错误")
	case 401:
		fmt.Println("未授权")
	case 404:
		fmt.Println("页面未找到") // 输出: 页面未找到
	case 500:
		fmt.Println("服务器内部错误")
	default:
		fmt.Printf("未知状态码: %d\n", statusCode)
	}
	
	// 10. 实际应用：用户权限检查
	fmt.Println("\n--- 实际应用：用户权限检查 ---")
	userRole := "admin"
	action := "delete"
	
	switch userRole {
	case "admin":
		fmt.Printf("管理员可以执行任何操作，包括%s\n", action) // 输出: 管理员可以执行任何操作，包括delete
	case "editor":
		switch action {
		case "read", "write", "update":
			fmt.Printf("编辑者可以执行%s操作\n", action)
		default:
			fmt.Printf("编辑者不能执行%s操作\n", action)
		}
	case "viewer":
		switch action {
		case "read":
			fmt.Printf("查看者可以执行%s操作\n", action)
		default:
			fmt.Printf("查看者不能执行%s操作\n", action)
		}
	default:
		fmt.Println("未知用户角色")
	}
	
	// 11. 实际应用：文件扩展名处理
	fmt.Println("\n--- 实际应用：文件扩展名处理 ---")
	filename := "document.pdf"
	
	// 获取文件扩展名（简化版本）
	var ext string
	for i := len(filename) - 1; i >= 0; i-- {
		if filename[i] == '.' {
			ext = filename[i+1:]
			break
		}
	}
	
	switch ext {
	case "txt", "md":
		fmt.Printf("文本文件: %s\n", filename)
	case "jpg", "png", "gif":
		fmt.Printf("图片文件: %s\n", filename)
	case "pdf":
		fmt.Printf("PDF文件: %s\n", filename) // 输出: PDF文件: document.pdf
	case "mp3", "wav":
		fmt.Printf("音频文件: %s\n", filename)
	case "mp4", "avi":
		fmt.Printf("视频文件: %s\n", filename)
	default:
		fmt.Printf("未知文件类型: %s\n", filename)
	}
	
	// 12. 实际应用：计算器操作
	fmt.Println("\n--- 实际应用：计算器操作 ---")
	a, b := 10, 3
	operator := "/"
	
	switch operator {
	case "+":
		fmt.Printf("%d + %d = %d\n", a, b, a+b)
	case "-":
		fmt.Printf("%d - %d = %d\n", a, b, a-b)
	case "*":
		fmt.Printf("%d * %d = %d\n", a, b, a*b)
	case "/":
		if b != 0 {
			fmt.Printf("%d / %d = %.2f\n", a, b, float64(a)/float64(b)) // 输出: 10 / 3 = 3.33
		} else {
			fmt.Println("除数不能为零")
		}
	case "%":
		if b != 0 {
			fmt.Printf("%d %% %d = %d\n", a, b, a%b)
		} else {
			fmt.Println("除数不能为零")
		}
	default:
		fmt.Printf("不支持的操作符: %s\n", operator)
	}
	
	// 13. switch vs if-else性能比较说明
	fmt.Println("\n--- switch vs if-else ---")
	fmt.Println("switch语句的优势:")
	fmt.Println("1. 代码更简洁易读")
	fmt.Println("2. 编译器可能进行优化")
	fmt.Println("3. 支持多个值匹配")
	fmt.Println("4. 支持类型switch")
	fmt.Println("5. 默认不会fall through（与C语言不同）")
}

/*
运行命令: go run 02_control_flow/03_switch.go

Go语言switch语句特点：

1. 不需要break语句，默认不会fall through
2. 支持多个值在一个case中：case 1, 2, 3:
3. 支持表达式case：case x > 10:
4. 支持类型switch：switch v := x.(type)
5. 支持初始化语句：switch init; condition
6. 可以没有表达式：switch { }（等价于switch true）
7. fallthrough关键字可以强制执行下一个case

语法格式：
switch expression {
case value1:
    // 代码
case value2, value3:
    // 代码
default:
    // 代码
}

类型switch语法：
switch v := x.(type) {
case int:
    // v是int类型
case string:
    // v是string类型
}

最佳实践：
1. 优先使用switch而不是长的if-else链
2. 合理使用default分支处理异常情况
3. 类型switch用于处理interface{}类型
4. 谨慎使用fallthrough，确保逻辑正确
*/
