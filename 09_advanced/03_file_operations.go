// 03_file_operations.go - Go语言文件操作
// 这个文件演示了Go语言中文件和目录操作的各种方法

package main

import (
	"bufio"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// 1. 基本文件操作
func basicFileOperations() {
	fmt.Println("--- 基本文件操作 ---")
	
	filename := "example.txt"
	content := "Hello, Go文件操作!\n这是第二行。\n这是第三行。"
	
	// 写入文件
	err := os.WriteFile(filename, []byte(content), 0644)
	if err != nil {
		fmt.Printf("写入文件失败: %v\n", err)
		return
	}
	fmt.Printf("成功写入文件: %s\n", filename)
	
	// 读取文件
	data, err := os.ReadFile(filename)
	if err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		return
	}
	fmt.Printf("文件内容:\n%s\n", string(data))
	
	// 获取文件信息
	fileInfo, err := os.Stat(filename)
	if err != nil {
		fmt.Printf("获取文件信息失败: %v\n", err)
		return
	}
	
	fmt.Printf("文件信息:\n")
	fmt.Printf("  名称: %s\n", fileInfo.Name())
	fmt.Printf("  大小: %d 字节\n", fileInfo.Size())
	fmt.Printf("  模式: %s\n", fileInfo.Mode())
	fmt.Printf("  修改时间: %s\n", fileInfo.ModTime().Format("2006-01-02 15:04:05"))
	fmt.Printf("  是否为目录: %t\n", fileInfo.IsDir())
	
	// 删除文件
	err = os.Remove(filename)
	if err != nil {
		fmt.Printf("删除文件失败: %v\n", err)
	} else {
		fmt.Printf("成功删除文件: %s\n", filename)
	}
}

// 2. 使用File类型进行文件操作
func fileTypeOperations() {
	fmt.Println("\n--- File类型操作 ---")
	
	filename := "file_example.txt"
	
	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		fmt.Printf("创建文件失败: %v\n", err)
		return
	}
	defer file.Close() // 确保文件被关闭
	
	// 写入数据
	lines := []string{
		"第一行数据",
		"第二行数据",
		"第三行数据",
	}
	
	for i, line := range lines {
		n, err := file.WriteString(fmt.Sprintf("%d: %s\n", i+1, line))
		if err != nil {
			fmt.Printf("写入失败: %v\n", err)
			return
		}
		fmt.Printf("写入了 %d 字节\n", n)
	}
	
	// 同步到磁盘
	err = file.Sync()
	if err != nil {
		fmt.Printf("同步失败: %v\n", err)
	}
	
	// 关闭文件
	file.Close()
	
	// 重新打开文件读取
	file, err = os.Open(filename)
	if err != nil {
		fmt.Printf("打开文件失败: %v\n", err)
		return
	}
	defer file.Close()
	
	// 读取文件内容
	fmt.Println("文件内容:")
	buffer := make([]byte, 1024)
	for {
		n, err := file.Read(buffer)
		if err == io.EOF {
			break
		}
		if err != nil {
			fmt.Printf("读取失败: %v\n", err)
			break
		}
		fmt.Print(string(buffer[:n]))
	}
	
	// 清理
	os.Remove(filename)
}

// 3. 缓冲读写
func bufferedOperations() {
	fmt.Println("\n--- 缓冲读写 ---")
	
	filename := "buffered_example.txt"
	
	// 缓冲写入
	file, err := os.Create(filename)
	if err != nil {
		fmt.Printf("创建文件失败: %v\n", err)
		return
	}
	defer file.Close()
	
	writer := bufio.NewWriter(file)
	defer writer.Flush() // 确保缓冲区被刷新
	
	for i := 1; i <= 5; i++ {
		line := fmt.Sprintf("缓冲写入第 %d 行\n", i)
		_, err := writer.WriteString(line)
		if err != nil {
			fmt.Printf("写入失败: %v\n", err)
			return
		}
	}
	
	// 手动刷新缓冲区
	err = writer.Flush()
	if err != nil {
		fmt.Printf("刷新缓冲区失败: %v\n", err)
	}
	
	file.Close()
	
	// 缓冲读取
	file, err = os.Open(filename)
	if err != nil {
		fmt.Printf("打开文件失败: %v\n", err)
		return
	}
	defer file.Close()
	
	reader := bufio.NewReader(file)
	fmt.Println("逐行读取:")
	
	lineNum := 1
	for {
		line, err := reader.ReadString('\n')
		if err == io.EOF {
			if len(line) > 0 {
				fmt.Printf("%d: %s", lineNum, line)
			}
			break
		}
		if err != nil {
			fmt.Printf("读取失败: %v\n", err)
			break
		}
		fmt.Printf("%d: %s", lineNum, line)
		lineNum++
	}
	
	// 清理
	os.Remove(filename)
}

// 4. 文件追加操作
func appendOperations() {
	fmt.Println("\n--- 文件追加操作 ---")
	
	filename := "append_example.txt"
	
	// 初始写入
	err := os.WriteFile(filename, []byte("初始内容\n"), 0644)
	if err != nil {
		fmt.Printf("初始写入失败: %v\n", err)
		return
	}
	
	// 以追加模式打开文件
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("打开文件失败: %v\n", err)
		return
	}
	defer file.Close()
	
	// 追加内容
	appendLines := []string{
		"追加的第一行",
		"追加的第二行",
		"追加的第三行",
	}
	
	for _, line := range appendLines {
		_, err := file.WriteString(line + "\n")
		if err != nil {
			fmt.Printf("追加失败: %v\n", err)
			return
		}
	}
	
	file.Close()
	
	// 读取完整文件内容
	content, err := os.ReadFile(filename)
	if err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		return
	}
	
	fmt.Println("完整文件内容:")
	fmt.Print(string(content))
	
	// 清理
	os.Remove(filename)
}

// 5. 目录操作
func directoryOperations() {
	fmt.Println("\n--- 目录操作 ---")
	
	dirName := "example_dir"
	
	// 创建目录
	err := os.Mkdir(dirName, 0755)
	if err != nil {
		fmt.Printf("创建目录失败: %v\n", err)
		return
	}
	fmt.Printf("成功创建目录: %s\n", dirName)
	
	// 创建嵌套目录
	nestedDir := filepath.Join(dirName, "nested", "deep")
	err = os.MkdirAll(nestedDir, 0755)
	if err != nil {
		fmt.Printf("创建嵌套目录失败: %v\n", err)
		return
	}
	fmt.Printf("成功创建嵌套目录: %s\n", nestedDir)
	
	// 在目录中创建文件
	files := []string{"file1.txt", "file2.txt", "file3.txt"}
	for _, filename := range files {
		filePath := filepath.Join(dirName, filename)
		content := fmt.Sprintf("这是文件 %s 的内容", filename)
		err := os.WriteFile(filePath, []byte(content), 0644)
		if err != nil {
			fmt.Printf("创建文件 %s 失败: %v\n", filename, err)
		} else {
			fmt.Printf("创建文件: %s\n", filePath)
		}
	}
	
	// 读取目录内容
	entries, err := os.ReadDir(dirName)
	if err != nil {
		fmt.Printf("读取目录失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n目录 %s 的内容:\n", dirName)
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			fmt.Printf("获取文件信息失败: %v\n", err)
			continue
		}
		
		fileType := "文件"
		if entry.IsDir() {
			fileType = "目录"
		}
		
		fmt.Printf("  %s: %s (%d 字节, %s)\n",
			fileType, entry.Name(), info.Size(),
			info.ModTime().Format("2006-01-02 15:04:05"))
	}
	
	// 递归删除目录
	err = os.RemoveAll(dirName)
	if err != nil {
		fmt.Printf("删除目录失败: %v\n", err)
	} else {
		fmt.Printf("成功删除目录: %s\n", dirName)
	}
}

// 6. 文件路径操作
func pathOperations() {
	fmt.Println("\n--- 文件路径操作 ---")
	
	paths := []string{
		"/home/<USER>/documents/file.txt",
		"./relative/path/file.go",
		"../parent/file.md",
		"simple_file.txt",
	}
	
	for _, path := range paths {
		fmt.Printf("路径: %s\n", path)
		fmt.Printf("  目录: %s\n", filepath.Dir(path))
		fmt.Printf("  文件名: %s\n", filepath.Base(path))
		fmt.Printf("  扩展名: %s\n", filepath.Ext(path))
		fmt.Printf("  是否绝对路径: %t\n", filepath.IsAbs(path))
		
		// 清理路径
		cleaned := filepath.Clean(path)
		fmt.Printf("  清理后: %s\n", cleaned)
		
		// 分割路径
		dir, file := filepath.Split(path)
		fmt.Printf("  分割: 目录='%s', 文件='%s'\n", dir, file)
		
		fmt.Println()
	}
	
	// 构建路径
	constructedPath := filepath.Join("home", "user", "documents", "file.txt")
	fmt.Printf("构建的路径: %s\n", constructedPath)
	
	// 获取当前工作目录
	if cwd, err := os.Getwd(); err == nil {
		fmt.Printf("当前工作目录: %s\n", cwd)
	}
}

// 7. 文件遍历
func walkDirectory() {
	fmt.Println("\n--- 文件遍历 ---")
	
	// 创建测试目录结构
	testDir := "walk_test"
	dirs := []string{
		testDir,
		filepath.Join(testDir, "subdir1"),
		filepath.Join(testDir, "subdir2"),
		filepath.Join(testDir, "subdir1", "nested"),
	}
	
	for _, dir := range dirs {
		os.MkdirAll(dir, 0755)
	}
	
	// 创建测试文件
	files := []string{
		filepath.Join(testDir, "root.txt"),
		filepath.Join(testDir, "subdir1", "file1.txt"),
		filepath.Join(testDir, "subdir1", "file2.go"),
		filepath.Join(testDir, "subdir2", "file3.md"),
		filepath.Join(testDir, "subdir1", "nested", "deep.txt"),
	}
	
	for _, file := range files {
		content := fmt.Sprintf("内容: %s", filepath.Base(file))
		os.WriteFile(file, []byte(content), 0644)
	}
	
	// 使用filepath.Walk遍历
	fmt.Printf("遍历目录 %s:\n", testDir)
	err := filepath.Walk(testDir, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		indent := strings.Repeat("  ", strings.Count(path, string(filepath.Separator))-1)
		fileType := "文件"
		if info.IsDir() {
			fileType = "目录"
		}
		
		fmt.Printf("%s%s: %s (%d 字节)\n", indent, fileType, info.Name(), info.Size())
		return nil
	})
	
	if err != nil {
		fmt.Printf("遍历失败: %v\n", err)
	}
	
	// 使用filepath.WalkDir遍历（Go 1.16+，更高效）
	fmt.Printf("\n使用WalkDir遍历 %s:\n", testDir)
	err = filepath.WalkDir(testDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		indent := strings.Repeat("  ", strings.Count(path, string(filepath.Separator))-1)
		fileType := "文件"
		if d.IsDir() {
			fileType = "目录"
		}
		
		fmt.Printf("%s%s: %s\n", indent, fileType, d.Name())
		return nil
	})
	
	if err != nil {
		fmt.Printf("WalkDir遍历失败: %v\n", err)
	}
	
	// 清理
	os.RemoveAll(testDir)
}

// 8. 文件权限和属性
func filePermissions() {
	fmt.Println("\n--- 文件权限和属性 ---")
	
	filename := "permission_test.txt"
	
	// 创建文件并设置权限
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		fmt.Printf("创建文件失败: %v\n", err)
		return
	}
	file.WriteString("权限测试文件")
	file.Close()
	
	// 获取文件信息
	info, err := os.Stat(filename)
	if err != nil {
		fmt.Printf("获取文件信息失败: %v\n", err)
		return
	}
	
	fmt.Printf("文件: %s\n", filename)
	fmt.Printf("权限: %s\n", info.Mode())
	fmt.Printf("权限(八进制): %o\n", info.Mode().Perm())
	
	// 修改文件权限
	err = os.Chmod(filename, 0644)
	if err != nil {
		fmt.Printf("修改权限失败: %v\n", err)
	} else {
		fmt.Println("权限修改成功")
		
		// 再次检查权限
		if info, err := os.Stat(filename); err == nil {
			fmt.Printf("新权限: %s\n", info.Mode())
		}
	}
	
	// 修改文件时间
	newTime := time.Now().Add(-24 * time.Hour) // 一天前
	err = os.Chtimes(filename, newTime, newTime)
	if err != nil {
		fmt.Printf("修改时间失败: %v\n", err)
	} else {
		fmt.Println("文件时间修改成功")
		
		if info, err := os.Stat(filename); err == nil {
			fmt.Printf("新修改时间: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
		}
	}
	
	// 清理
	os.Remove(filename)
}

// 9. 临时文件和目录
func temporaryFiles() {
	fmt.Println("\n--- 临时文件和目录 ---")
	
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "example_*.txt")
	if err != nil {
		fmt.Printf("创建临时文件失败: %v\n", err)
		return
	}
	defer os.Remove(tempFile.Name()) // 清理临时文件
	defer tempFile.Close()
	
	fmt.Printf("临时文件路径: %s\n", tempFile.Name())
	
	// 写入临时文件
	content := "这是临时文件的内容\n临时文件会在程序结束时被删除"
	_, err = tempFile.WriteString(content)
	if err != nil {
		fmt.Printf("写入临时文件失败: %v\n", err)
		return
	}
	
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "example_dir_*")
	if err != nil {
		fmt.Printf("创建临时目录失败: %v\n", err)
		return
	}
	defer os.RemoveAll(tempDir) // 清理临时目录
	
	fmt.Printf("临时目录路径: %s\n", tempDir)
	
	// 在临时目录中创建文件
	tempFilePath := filepath.Join(tempDir, "temp_file.txt")
	err = os.WriteFile(tempFilePath, []byte("临时目录中的文件"), 0644)
	if err != nil {
		fmt.Printf("在临时目录创建文件失败: %v\n", err)
	} else {
		fmt.Printf("在临时目录创建文件: %s\n", tempFilePath)
	}
	
	// 获取系统临时目录
	systemTempDir := os.TempDir()
	fmt.Printf("系统临时目录: %s\n", systemTempDir)
}

func main() {
	fmt.Println("=== Go语言文件操作示例 ===")
	
	// 1. 基本文件操作
	basicFileOperations()
	
	// 2. File类型操作
	fileTypeOperations()
	
	// 3. 缓冲读写
	bufferedOperations()
	
	// 4. 文件追加操作
	appendOperations()
	
	// 5. 目录操作
	directoryOperations()
	
	// 6. 文件路径操作
	pathOperations()
	
	// 7. 文件遍历
	walkDirectory()
	
	// 8. 文件权限和属性
	filePermissions()
	
	// 9. 临时文件和目录
	temporaryFiles()
	
	// 10. 文件操作最佳实践
	fmt.Println("\n--- 文件操作最佳实践 ---")
	
	fmt.Println("文件操作最佳实践:")
	fmt.Println("1. 总是检查错误")
	fmt.Println("2. 使用defer确保文件被关闭")
	fmt.Println("3. 使用适当的文件权限")
	fmt.Println("4. 对大文件使用缓冲读写")
	fmt.Println("5. 使用filepath包处理路径")
	fmt.Println("6. 处理不同操作系统的路径差异")
	fmt.Println("7. 使用临时文件进行安全操作")
	fmt.Println("8. 及时清理临时文件和目录")
	fmt.Println("9. 考虑并发访问的文件锁定")
	fmt.Println("10. 使用适当的I/O模式(同步/异步)")
}

/*
运行命令: conda activate golang && go run 09_advanced/03_file_operations.go

Go语言文件操作总结：

1. 基本文件操作：
   - os.ReadFile(): 读取整个文件
   - os.WriteFile(): 写入整个文件
   - os.Create(): 创建文件
   - os.Open(): 打开文件读取
   - os.OpenFile(): 以指定模式打开文件

2. 文件信息：
   - os.Stat(): 获取文件信息
   - FileInfo接口: Name(), Size(), Mode(), ModTime(), IsDir()
   - 文件权限和属性操作

3. 缓冲I/O：
   - bufio.NewReader(): 缓冲读取
   - bufio.NewWriter(): 缓冲写入
   - 提高大文件操作性能

4. 目录操作：
   - os.Mkdir(): 创建目录
   - os.MkdirAll(): 创建嵌套目录
   - os.ReadDir(): 读取目录内容
   - os.Remove()/RemoveAll(): 删除文件/目录

5. 路径操作：
   - filepath包: 跨平台路径处理
   - Join(), Dir(), Base(), Ext()
   - Clean(), Abs(), Rel()

6. 文件遍历：
   - filepath.Walk(): 递归遍历目录
   - filepath.WalkDir(): 更高效的遍历(Go 1.16+)
   - 自定义遍历逻辑

7. 临时文件：
   - os.CreateTemp(): 创建临时文件
   - os.MkdirTemp(): 创建临时目录
   - 自动清理机制

8. 最佳实践：
   - 错误处理
   - 资源清理(defer)
   - 适当的权限设置
   - 缓冲I/O优化
   - 跨平台兼容性

9. 高级特性：
   - 文件锁定
   - 内存映射文件
   - 异步I/O
   - 文件监控

10. 安全考虑：
    - 路径遍历攻击防护
    - 权限检查
    - 临时文件安全
    - 并发访问控制
*/
