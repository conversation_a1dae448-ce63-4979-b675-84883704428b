// 04_json_xml.go - Go语言JSON和XML处理
// 这个文件演示了Go语言中JSON和XML的编码、解码和处理

package main

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"os"
	"strings"
	"time"
)

// 1. JSON基础结构体
type Person struct {
	Name      string    `json:"name"`
	Age       int       `json:"age"`
	Email     string    `json:"email,omitempty"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	Tags      []string  `json:"tags,omitempty"`
}

// 2. 嵌套结构体
type Address struct {
	Street  string `json:"street"`
	City    string `json:"city"`
	Country string `json:"country"`
	ZipCode string `json:"zip_code,omitempty"`
}

type User struct {
	ID       int     `json:"id"`
	Name     string  `json:"name"`
	Email    string  `json:"email"`
	Address  Address `json:"address"`
	Hobbies  []string `json:"hobbies,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// 3. 自定义JSON序列化
type CustomTime struct {
	time.Time
}

func (ct CustomTime) MarshalJSON() ([]byte, error) {
	return json.Marshal(ct.Time.Format("2006-01-02 15:04:05"))
}

func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	var timeStr string
	if err := json.Unmarshal(data, &timeStr); err != nil {
		return err
	}
	
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		return err
	}
	
	ct.Time = t
	return nil
}

type Event struct {
	Name      string     `json:"name"`
	Timestamp CustomTime `json:"timestamp"`
	Data      string     `json:"data"`
}

// 4. XML结构体
type Book struct {
	XMLName xml.Name `xml:"book"`
	ID      int      `xml:"id,attr"`
	Title   string   `xml:"title"`
	Author  Author   `xml:"author"`
	Price   float64  `xml:"price"`
	Tags    []string `xml:"tags>tag"`
}

type Author struct {
	Name  string `xml:"name"`
	Email string `xml:"email,omitempty"`
}

type Library struct {
	XMLName xml.Name `xml:"library"`
	Name    string   `xml:"name,attr"`
	Books   []Book   `xml:"books>book"`
}

// 5. JSON基础操作
func jsonBasicOperations() {
	fmt.Println("--- JSON基础操作 ---")
	
	// 创建Person实例
	person := Person{
		Name:      "张三",
		Age:       25,
		Email:     "<EMAIL>",
		IsActive:  true,
		CreatedAt: time.Now(),
		Tags:      []string{"developer", "golang", "backend"},
	}
	
	// 序列化为JSON
	jsonData, err := json.Marshal(person)
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("序列化结果:\n%s\n", string(jsonData))
	
	// 美化输出
	prettyJSON, err := json.MarshalIndent(person, "", "  ")
	if err != nil {
		fmt.Printf("美化JSON失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n美化输出:\n%s\n", string(prettyJSON))
	
	// 反序列化
	var decodedPerson Person
	err = json.Unmarshal(jsonData, &decodedPerson)
	if err != nil {
		fmt.Printf("JSON反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n反序列化结果:\n")
	fmt.Printf("姓名: %s\n", decodedPerson.Name)
	fmt.Printf("年龄: %d\n", decodedPerson.Age)
	fmt.Printf("邮箱: %s\n", decodedPerson.Email)
	fmt.Printf("是否活跃: %t\n", decodedPerson.IsActive)
	fmt.Printf("创建时间: %s\n", decodedPerson.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("标签: %v\n", decodedPerson.Tags)
}

// 6. 嵌套结构体JSON处理
func nestedJSONOperations() {
	fmt.Println("\n--- 嵌套结构体JSON处理 ---")
	
	user := User{
		ID:    1,
		Name:  "李四",
		Email: "<EMAIL>",
		Address: Address{
			Street:  "中山路123号",
			City:    "北京",
			Country: "中国",
			ZipCode: "100000",
		},
		Hobbies: []string{"阅读", "编程", "旅行"},
		Metadata: map[string]interface{}{
			"level":        "senior",
			"score":        95.5,
			"verified":     true,
			"last_login":   "2023-12-01",
		},
	}
	
	// 序列化
	jsonData, err := json.MarshalIndent(user, "", "  ")
	if err != nil {
		fmt.Printf("序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("嵌套结构体JSON:\n%s\n", string(jsonData))
	
	// 反序列化
	var decodedUser User
	err = json.Unmarshal(jsonData, &decodedUser)
	if err != nil {
		fmt.Printf("反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n反序列化结果:\n")
	fmt.Printf("用户ID: %d\n", decodedUser.ID)
	fmt.Printf("姓名: %s\n", decodedUser.Name)
	fmt.Printf("地址: %s, %s, %s\n", 
		decodedUser.Address.Street, 
		decodedUser.Address.City, 
		decodedUser.Address.Country)
	fmt.Printf("爱好: %v\n", decodedUser.Hobbies)
	fmt.Printf("元数据: %v\n", decodedUser.Metadata)
}

// 7. 自定义JSON序列化
func customJSONSerialization() {
	fmt.Println("\n--- 自定义JSON序列化 ---")
	
	event := Event{
		Name:      "系统启动",
		Timestamp: CustomTime{time.Now()},
		Data:      "系统成功启动",
	}
	
	// 序列化
	jsonData, err := json.MarshalIndent(event, "", "  ")
	if err != nil {
		fmt.Printf("序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("自定义序列化结果:\n%s\n", string(jsonData))
	
	// 反序列化
	var decodedEvent Event
	err = json.Unmarshal(jsonData, &decodedEvent)
	if err != nil {
		fmt.Printf("反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n反序列化结果:\n")
	fmt.Printf("事件名称: %s\n", decodedEvent.Name)
	fmt.Printf("时间戳: %s\n", decodedEvent.Timestamp.Format("2006-01-02 15:04:05"))
	fmt.Printf("数据: %s\n", decodedEvent.Data)
}

// 8. 动态JSON处理
func dynamicJSONProcessing() {
	fmt.Println("\n--- 动态JSON处理 ---")
	
	// 处理未知结构的JSON
	jsonStr := `{
		"name": "动态数据",
		"count": 42,
		"active": true,
		"items": ["item1", "item2", "item3"],
		"config": {
			"timeout": 30,
			"retry": 3
		}
	}`
	
	// 解析到interface{}
	var data interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		fmt.Printf("解析失败: %v\n", err)
		return
	}
	
	// 类型断言访问数据
	if dataMap, ok := data.(map[string]interface{}); ok {
		fmt.Printf("名称: %v\n", dataMap["name"])
		fmt.Printf("计数: %v\n", dataMap["count"])
		fmt.Printf("活跃: %v\n", dataMap["active"])
		
		if items, ok := dataMap["items"].([]interface{}); ok {
			fmt.Printf("项目: ")
			for _, item := range items {
				fmt.Printf("%v ", item)
			}
			fmt.Println()
		}
		
		if config, ok := dataMap["config"].(map[string]interface{}); ok {
			fmt.Printf("配置: timeout=%v, retry=%v\n", config["timeout"], config["retry"])
		}
	}
	
	// 使用json.RawMessage延迟解析
	type PartialData struct {
		Name   string          `json:"name"`
		Config json.RawMessage `json:"config"`
	}
	
	var partial PartialData
	err = json.Unmarshal([]byte(jsonStr), &partial)
	if err != nil {
		fmt.Printf("部分解析失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n部分解析结果:\n")
	fmt.Printf("名称: %s\n", partial.Name)
	fmt.Printf("原始配置: %s\n", string(partial.Config))
}

// 9. XML基础操作
func xmlBasicOperations() {
	fmt.Println("\n--- XML基础操作 ---")
	
	// 创建Book实例
	book := Book{
		ID:    1,
		Title: "Go语言编程",
		Author: Author{
			Name:  "作者姓名",
			Email: "<EMAIL>",
		},
		Price: 59.99,
		Tags:  []string{"编程", "Go", "后端"},
	}
	
	// 序列化为XML
	xmlData, err := xml.MarshalIndent(book, "", "  ")
	if err != nil {
		fmt.Printf("XML序列化失败: %v\n", err)
		return
	}
	
	// 添加XML头
	xmlString := xml.Header + string(xmlData)
	fmt.Printf("XML序列化结果:\n%s\n", xmlString)
	
	// 反序列化
	var decodedBook Book
	err = xml.Unmarshal(xmlData, &decodedBook)
	if err != nil {
		fmt.Printf("XML反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("\nXML反序列化结果:\n")
	fmt.Printf("ID: %d\n", decodedBook.ID)
	fmt.Printf("标题: %s\n", decodedBook.Title)
	fmt.Printf("作者: %s (%s)\n", decodedBook.Author.Name, decodedBook.Author.Email)
	fmt.Printf("价格: %.2f\n", decodedBook.Price)
	fmt.Printf("标签: %v\n", decodedBook.Tags)
}

// 10. 复杂XML处理
func complexXMLOperations() {
	fmt.Println("\n--- 复杂XML处理 ---")
	
	library := Library{
		Name: "市图书馆",
		Books: []Book{
			{
				ID:    1,
				Title: "Go语言实战",
				Author: Author{Name: "张三", Email: "<EMAIL>"},
				Price: 68.00,
				Tags:  []string{"编程", "Go"},
			},
			{
				ID:    2,
				Title: "数据结构与算法",
				Author: Author{Name: "李四"},
				Price: 75.50,
				Tags:  []string{"算法", "数据结构"},
			},
		},
	}
	
	// 序列化
	xmlData, err := xml.MarshalIndent(library, "", "  ")
	if err != nil {
		fmt.Printf("序列化失败: %v\n", err)
		return
	}
	
	xmlString := xml.Header + string(xmlData)
	fmt.Printf("图书馆XML:\n%s\n", xmlString)
	
	// 反序列化
	var decodedLibrary Library
	err = xml.Unmarshal(xmlData, &decodedLibrary)
	if err != nil {
		fmt.Printf("反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n反序列化结果:\n")
	fmt.Printf("图书馆: %s\n", decodedLibrary.Name)
	fmt.Printf("图书数量: %d\n", len(decodedLibrary.Books))
	
	for i, book := range decodedLibrary.Books {
		fmt.Printf("图书 %d:\n", i+1)
		fmt.Printf("  ID: %d\n", book.ID)
		fmt.Printf("  标题: %s\n", book.Title)
		fmt.Printf("  作者: %s\n", book.Author.Name)
		fmt.Printf("  价格: %.2f\n", book.Price)
		fmt.Printf("  标签: %v\n", book.Tags)
	}
}

// 11. 文件读写JSON/XML
func fileOperations() {
	fmt.Println("\n--- 文件读写JSON/XML ---")
	
	// 准备数据
	users := []User{
		{
			ID:    1,
			Name:  "用户1",
			Email: "<EMAIL>",
			Address: Address{
				Street:  "街道1",
				City:    "城市1",
				Country: "中国",
			},
			Hobbies: []string{"阅读", "运动"},
		},
		{
			ID:    2,
			Name:  "用户2",
			Email: "<EMAIL>",
			Address: Address{
				Street:  "街道2",
				City:    "城市2",
				Country: "中国",
			},
			Hobbies: []string{"音乐", "旅行"},
		},
	}
	
	// 写入JSON文件
	jsonFile := "users.json"
	jsonData, err := json.MarshalIndent(users, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}
	
	err = os.WriteFile(jsonFile, jsonData, 0644)
	if err != nil {
		fmt.Printf("写入JSON文件失败: %v\n", err)
		return
	}
	fmt.Printf("成功写入JSON文件: %s\n", jsonFile)
	
	// 从JSON文件读取
	jsonData, err = os.ReadFile(jsonFile)
	if err != nil {
		fmt.Printf("读取JSON文件失败: %v\n", err)
		return
	}
	
	var loadedUsers []User
	err = json.Unmarshal(jsonData, &loadedUsers)
	if err != nil {
		fmt.Printf("JSON反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("从JSON文件加载了 %d 个用户\n", len(loadedUsers))
	
	// 写入XML文件
	library := Library{
		Name: "示例图书馆",
		Books: []Book{
			{ID: 1, Title: "书籍1", Author: Author{Name: "作者1"}, Price: 29.99},
			{ID: 2, Title: "书籍2", Author: Author{Name: "作者2"}, Price: 39.99},
		},
	}
	
	xmlFile := "library.xml"
	xmlData, err := xml.MarshalIndent(library, "", "  ")
	if err != nil {
		fmt.Printf("XML序列化失败: %v\n", err)
		return
	}
	
	xmlContent := xml.Header + string(xmlData)
	err = os.WriteFile(xmlFile, []byte(xmlContent), 0644)
	if err != nil {
		fmt.Printf("写入XML文件失败: %v\n", err)
		return
	}
	fmt.Printf("成功写入XML文件: %s\n", xmlFile)
	
	// 从XML文件读取
	xmlData, err = os.ReadFile(xmlFile)
	if err != nil {
		fmt.Printf("读取XML文件失败: %v\n", err)
		return
	}
	
	var loadedLibrary Library
	err = xml.Unmarshal(xmlData, &loadedLibrary)
	if err != nil {
		fmt.Printf("XML反序列化失败: %v\n", err)
		return
	}
	
	fmt.Printf("从XML文件加载了图书馆: %s，包含 %d 本书\n", 
		loadedLibrary.Name, len(loadedLibrary.Books))
	
	// 清理文件
	os.Remove(jsonFile)
	os.Remove(xmlFile)
}

// 12. JSON/XML处理技巧
func processingTips() {
	fmt.Println("\n--- JSON/XML处理技巧 ---")
	
	// JSON标签技巧
	type Product struct {
		ID          int     `json:"id"`
		Name        string  `json:"name"`
		Price       float64 `json:"price"`
		Description string  `json:"description,omitempty"` // 空值时省略
		Internal    string  `json:"-"`                     // 不序列化
		Category    string  `json:"category,string"`       // 强制字符串
	}
	
	product := Product{
		ID:       1,
		Name:     "商品1",
		Price:    99.99,
		Internal: "内部数据",
		Category: "electronics",
	}
	
	jsonData, _ := json.MarshalIndent(product, "", "  ")
	fmt.Printf("产品JSON (注意omitempty和-标签):\n%s\n", string(jsonData))
	
	// 处理JSON中的数字类型
	jsonStr := `{"count": "123", "price": 45.67}`
	
	// 使用json.Number处理数字
	decoder := json.NewDecoder(strings.NewReader(jsonStr))
	decoder.UseNumber()
	
	var data map[string]interface{}
	decoder.Decode(&data)
	
	if count, ok := data["count"].(json.Number); ok {
		if intVal, err := count.Int64(); err == nil {
			fmt.Printf("计数 (整数): %d\n", intVal)
		}
	}
	
	if price, ok := data["price"].(json.Number); ok {
		if floatVal, err := price.Float64(); err == nil {
			fmt.Printf("价格 (浮点): %.2f\n", floatVal)
		}
	}
}

func main() {
	fmt.Println("=== Go语言JSON和XML处理示例 ===")
	
	// 1. JSON基础操作
	jsonBasicOperations()
	
	// 2. 嵌套结构体JSON处理
	nestedJSONOperations()
	
	// 3. 自定义JSON序列化
	customJSONSerialization()
	
	// 4. 动态JSON处理
	dynamicJSONProcessing()
	
	// 5. XML基础操作
	xmlBasicOperations()
	
	// 6. 复杂XML处理
	complexXMLOperations()
	
	// 7. 文件读写JSON/XML
	fileOperations()
	
	// 8. JSON/XML处理技巧
	processingTips()
	
	// 9. 最佳实践
	fmt.Println("\n--- JSON/XML处理最佳实践 ---")
	
	fmt.Println("JSON处理最佳实践:")
	fmt.Println("1. 使用结构体标签控制序列化行为")
	fmt.Println("2. 处理空值和可选字段(omitempty)")
	fmt.Println("3. 自定义序列化方法处理特殊类型")
	fmt.Println("4. 使用json.RawMessage延迟解析")
	fmt.Println("5. 验证JSON数据的完整性")
	fmt.Println("6. 处理大型JSON时考虑流式解析")
	fmt.Println("7. 使用json.Number处理数字精度")
	
	fmt.Println("\nXML处理最佳实践:")
	fmt.Println("1. 使用xml.Name指定元素名称")
	fmt.Println("2. 合理使用属性和元素")
	fmt.Println("3. 处理命名空间")
	fmt.Println("4. 验证XML结构和内容")
	fmt.Println("5. 考虑XML的层次结构")
	fmt.Println("6. 处理CDATA和特殊字符")
	fmt.Println("7. 使用流式解析处理大型XML")
}

/*
运行命令: conda activate golang && go run 09_advanced/04_json_xml.go

Go语言JSON和XML处理总结：

1. JSON基础：
   - json.Marshal(): 序列化
   - json.Unmarshal(): 反序列化
   - json.MarshalIndent(): 美化输出
   - 结构体标签控制序列化

2. JSON标签：
   - `json:"name"`: 指定字段名
   - `json:",omitempty"`: 空值时省略
   - `json:"-"`: 不序列化
   - `json:",string"`: 强制字符串

3. 自定义序列化：
   - 实现MarshalJSON()方法
   - 实现UnmarshalJSON()方法
   - 处理特殊类型和格式

4. 动态JSON：
   - interface{}处理未知结构
   - json.RawMessage延迟解析
   - 类型断言访问数据
   - json.Number处理数字

5. XML基础：
   - xml.Marshal(): 序列化
   - xml.Unmarshal(): 反序列化
   - xml.MarshalIndent(): 美化输出
   - XML头和命名空间

6. XML标签：
   - `xml:"name"`: 指定元素名
   - `xml:",attr"`: 作为属性
   - `xml:",omitempty"`: 空值省略
   - `xml:"parent>child"`: 嵌套元素

7. 文件操作：
   - 读写JSON/XML文件
   - 错误处理和验证
   - 文件格式化
   - 批量处理

8. 最佳实践：
   - 合理使用标签
   - 错误处理
   - 性能优化
   - 数据验证
   - 安全考虑

9. 高级特性：
   - 流式处理
   - 自定义编解码器
   - 性能优化
   - 内存管理

10. 应用场景：
    - API数据交换
    - 配置文件处理
    - 数据存储
    - 消息传递
    - 日志记录
*/
