// 02_generics.go - Go语言泛型 (Go 1.18+)
// 这个文件演示了Go语言中泛型的概念和使用

package main

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
)

// 1. 基本泛型函数
func Max[T comparable](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Min[T comparable](a, b T) T {
	if a < b {
		return a
	}
	return b
}

// 2. 泛型切片操作
func Contains[T comparable](slice []T, item T) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func Filter[T any](slice []T, predicate func(T) bool) []T {
	var result []T
	for _, v := range slice {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

func Map[T, U any](slice []T, mapper func(T) U) []U {
	result := make([]U, len(slice))
	for i, v := range slice {
		result[i] = mapper(v)
	}
	return result
}

func Reduce[T, U any](slice []T, initial U, reducer func(U, T) U) U {
	result := initial
	for _, v := range slice {
		result = reducer(result, v)
	}
	return result
}

// 3. 泛型数据结构
type Stack[T any] struct {
	items []T
}

func NewStack[T any]() *Stack[T] {
	return &Stack[T]{
		items: make([]T, 0),
	}
}

func (s *Stack[T]) Push(item T) {
	s.items = append(s.items, item)
}

func (s *Stack[T]) Pop() (T, bool) {
	if len(s.items) == 0 {
		var zero T
		return zero, false
	}
	
	index := len(s.items) - 1
	item := s.items[index]
	s.items = s.items[:index]
	return item, true
}

func (s *Stack[T]) Peek() (T, bool) {
	if len(s.items) == 0 {
		var zero T
		return zero, false
	}
	return s.items[len(s.items)-1], true
}

func (s *Stack[T]) IsEmpty() bool {
	return len(s.items) == 0
}

func (s *Stack[T]) Size() int {
	return len(s.items)
}

// 4. 泛型映射
type SafeMap[K comparable, V any] struct {
	data map[K]V
}

func NewSafeMap[K comparable, V any]() *SafeMap[K, V] {
	return &SafeMap[K, V]{
		data: make(map[K]V),
	}
}

func (sm *SafeMap[K, V]) Set(key K, value V) {
	sm.data[key] = value
}

func (sm *SafeMap[K, V]) Get(key K) (V, bool) {
	value, exists := sm.data[key]
	return value, exists
}

func (sm *SafeMap[K, V]) Delete(key K) {
	delete(sm.data, key)
}

func (sm *SafeMap[K, V]) Keys() []K {
	keys := make([]K, 0, len(sm.data))
	for k := range sm.data {
		keys = append(keys, k)
	}
	return keys
}

func (sm *SafeMap[K, V]) Values() []V {
	values := make([]V, 0, len(sm.data))
	for _, v := range sm.data {
		values = append(values, v)
	}
	return values
}

// 5. 类型约束
type Numeric interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 |
		~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
		~float32 | ~float64
}

func Sum[T Numeric](numbers []T) T {
	var sum T
	for _, num := range numbers {
		sum += num
	}
	return sum
}

func Average[T Numeric](numbers []T) float64 {
	if len(numbers) == 0 {
		return 0
	}
	sum := Sum(numbers)
	return float64(sum) / float64(len(numbers))
}

// 6. 泛型接口
type Comparable[T any] interface {
	Compare(other T) int // -1: less, 0: equal, 1: greater
}

type Person struct {
	Name string
	Age  int
}

func (p Person) Compare(other Person) int {
	if p.Age < other.Age {
		return -1
	} else if p.Age > other.Age {
		return 1
	}
	return strings.Compare(p.Name, other.Name)
}

func Sort[T Comparable[T]](slice []T) {
	sort.Slice(slice, func(i, j int) bool {
		return slice[i].Compare(slice[j]) < 0
	})
}

// 7. 泛型方法
type Container[T any] struct {
	value T
}

func NewContainer[T any](value T) *Container[T] {
	return &Container[T]{value: value}
}

func (c *Container[T]) Get() T {
	return c.value
}

func (c *Container[T]) Set(value T) {
	c.value = value
}

func (c *Container[T]) Transform(fn func(T) T) {
	c.value = fn(c.value)
}

// 8. 多类型参数
type Pair[T, U any] struct {
	First  T
	Second U
}

func NewPair[T, U any](first T, second U) Pair[T, U] {
	return Pair[T, U]{First: first, Second: second}
}

func (p Pair[T, U]) String() string {
	return fmt.Sprintf("(%v, %v)", p.First, p.Second)
}

func Zip[T, U any](slice1 []T, slice2 []U) []Pair[T, U] {
	minLen := len(slice1)
	if len(slice2) < minLen {
		minLen = len(slice2)
	}
	
	result := make([]Pair[T, U], minLen)
	for i := 0; i < minLen; i++ {
		result[i] = NewPair(slice1[i], slice2[i])
	}
	return result
}

// 9. 泛型树结构
type TreeNode[T any] struct {
	Value    T
	Children []*TreeNode[T]
}

func NewTreeNode[T any](value T) *TreeNode[T] {
	return &TreeNode[T]{
		Value:    value,
		Children: make([]*TreeNode[T], 0),
	}
}

func (node *TreeNode[T]) AddChild(child *TreeNode[T]) {
	node.Children = append(node.Children, child)
}

func (node *TreeNode[T]) Traverse(visit func(T)) {
	visit(node.Value)
	for _, child := range node.Children {
		child.Traverse(visit)
	}
}

// 10. 实际应用：泛型缓存
type Cache[K comparable, V any] struct {
	data     map[K]V
	maxSize  int
	keyOrder []K
}

func NewCache[K comparable, V any](maxSize int) *Cache[K, V] {
	return &Cache[K, V]{
		data:     make(map[K]V),
		maxSize:  maxSize,
		keyOrder: make([]K, 0),
	}
}

func (c *Cache[K, V]) Put(key K, value V) {
	// 如果键已存在，更新值
	if _, exists := c.data[key]; exists {
		c.data[key] = value
		return
	}
	
	// 如果缓存已满，删除最旧的项
	if len(c.data) >= c.maxSize {
		oldestKey := c.keyOrder[0]
		delete(c.data, oldestKey)
		c.keyOrder = c.keyOrder[1:]
	}
	
	// 添加新项
	c.data[key] = value
	c.keyOrder = append(c.keyOrder, key)
}

func (c *Cache[K, V]) Get(key K) (V, bool) {
	value, exists := c.data[key]
	return value, exists
}

func (c *Cache[K, V]) Size() int {
	return len(c.data)
}

func main() {
	fmt.Println("=== Go语言泛型示例 ===")
	
	// 1. 基本泛型函数
	fmt.Println("\n--- 基本泛型函数 ---")
	
	fmt.Printf("Max(10, 20): %d\n", Max(10, 20))           // 输出: Max(10, 20): 20
	fmt.Printf("Max(3.14, 2.71): %.2f\n", Max(3.14, 2.71)) // 输出: Max(3.14, 2.71): 3.14
	fmt.Printf("Max(\"apple\", \"banana\"): %s\n", Max("apple", "banana")) // 输出: Max("apple", "banana"): banana
	
	fmt.Printf("Min(10, 20): %d\n", Min(10, 20))           // 输出: Min(10, 20): 10
	fmt.Printf("Min(3.14, 2.71): %.2f\n", Min(3.14, 2.71)) // 输出: Min(3.14, 2.71): 2.71
	
	// 2. 泛型切片操作
	fmt.Println("\n--- 泛型切片操作 ---")
	
	numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	words := []string{"apple", "banana", "cherry", "date"}
	
	fmt.Printf("Contains(numbers, 5): %t\n", Contains(numbers, 5))     // 输出: true
	fmt.Printf("Contains(words, \"grape\"): %t\n", Contains(words, "grape")) // 输出: false
	
	// 过滤偶数
	evenNumbers := Filter(numbers, func(n int) bool { return n%2 == 0 })
	fmt.Printf("偶数: %v\n", evenNumbers) // 输出: 偶数: [2 4 6 8 10]
	
	// 映射：数字转字符串
	numberStrings := Map(numbers[:5], func(n int) string { return strconv.Itoa(n) })
	fmt.Printf("数字转字符串: %v\n", numberStrings) // 输出: 数字转字符串: [1 2 3 4 5]
	
	// 归约：计算总和
	sum := Reduce(numbers[:5], 0, func(acc, n int) int { return acc + n })
	fmt.Printf("前5个数字的和: %d\n", sum) // 输出: 前5个数字的和: 15
	
	// 3. 泛型数据结构：栈
	fmt.Println("\n--- 泛型栈 ---")
	
	intStack := NewStack[int]()
	intStack.Push(1)
	intStack.Push(2)
	intStack.Push(3)
	
	fmt.Printf("栈大小: %d\n", intStack.Size()) // 输出: 栈大小: 3
	
	if value, ok := intStack.Peek(); ok {
		fmt.Printf("栈顶元素: %d\n", value) // 输出: 栈顶元素: 3
	}
	
	for !intStack.IsEmpty() {
		if value, ok := intStack.Pop(); ok {
			fmt.Printf("弹出: %d\n", value)
		}
	}
	
	// 字符串栈
	stringStack := NewStack[string]()
	stringStack.Push("hello")
	stringStack.Push("world")
	
	if value, ok := stringStack.Pop(); ok {
		fmt.Printf("弹出字符串: %s\n", value) // 输出: 弹出字符串: world
	}
	
	// 4. 泛型映射
	fmt.Println("\n--- 泛型映射 ---")
	
	userMap := NewSafeMap[string, Person]()
	userMap.Set("user1", Person{Name: "张三", Age: 25})
	userMap.Set("user2", Person{Name: "李四", Age: 30})
	
	if user, exists := userMap.Get("user1"); exists {
		fmt.Printf("用户1: %+v\n", user) // 输出: 用户1: {Name:张三 Age:25}
	}
	
	fmt.Printf("所有用户ID: %v\n", userMap.Keys()) // 输出: 所有用户ID: [user1 user2]
	
	// 5. 数值类型约束
	fmt.Println("\n--- 数值类型约束 ---")
	
	intNumbers := []int{1, 2, 3, 4, 5}
	floatNumbers := []float64{1.1, 2.2, 3.3, 4.4, 5.5}
	
	fmt.Printf("整数和: %d\n", Sum(intNumbers))           // 输出: 整数和: 15
	fmt.Printf("浮点数和: %.1f\n", Sum(floatNumbers))     // 输出: 浮点数和: 16.5
	fmt.Printf("整数平均值: %.2f\n", Average(intNumbers))   // 输出: 整数平均值: 3.00
	fmt.Printf("浮点数平均值: %.2f\n", Average(floatNumbers)) // 输出: 浮点数平均值: 3.30
	
	// 6. 泛型接口和排序
	fmt.Println("\n--- 泛型接口和排序 ---")
	
	people := []Person{
		{Name: "张三", Age: 25},
		{Name: "李四", Age: 30},
		{Name: "王五", Age: 20},
		{Name: "赵六", Age: 25},
	}
	
	fmt.Printf("排序前: %v\n", people)
	Sort(people)
	fmt.Printf("排序后: %v\n", people)
	
	// 7. 泛型容器
	fmt.Println("\n--- 泛型容器 ---")
	
	intContainer := NewContainer(42)
	fmt.Printf("容器值: %d\n", intContainer.Get()) // 输出: 容器值: 42
	
	intContainer.Set(100)
	fmt.Printf("设置后: %d\n", intContainer.Get()) // 输出: 设置后: 100
	
	intContainer.Transform(func(n int) int { return n * 2 })
	fmt.Printf("变换后: %d\n", intContainer.Get()) // 输出: 变换后: 200
	
	// 8. 多类型参数
	fmt.Println("\n--- 多类型参数 ---")
	
	pair1 := NewPair("name", 25)
	pair2 := NewPair(3.14, true)
	
	fmt.Printf("配对1: %s\n", pair1.String()) // 输出: 配对1: (name, 25)
	fmt.Printf("配对2: %s\n", pair2.String()) // 输出: 配对2: (3.14, true)
	
	names := []string{"Alice", "Bob", "Charlie"}
	ages := []int{25, 30, 35}
	pairs := Zip(names, ages)
	
	fmt.Printf("压缩结果: %v\n", pairs) // 输出: 压缩结果: [(Alice, 25) (Bob, 30) (Charlie, 35)]
	
	// 9. 泛型树结构
	fmt.Println("\n--- 泛型树结构 ---")
	
	root := NewTreeNode("root")
	child1 := NewTreeNode("child1")
	child2 := NewTreeNode("child2")
	grandchild := NewTreeNode("grandchild")
	
	root.AddChild(child1)
	root.AddChild(child2)
	child1.AddChild(grandchild)
	
	fmt.Print("树遍历: ")
	root.Traverse(func(value string) {
		fmt.Printf("%s ", value)
	})
	fmt.Println() // 输出: 树遍历: root child1 grandchild child2
	
	// 10. 泛型缓存
	fmt.Println("\n--- 泛型缓存 ---")
	
	cache := NewCache[string, int](3)
	
	cache.Put("a", 1)
	cache.Put("b", 2)
	cache.Put("c", 3)
	fmt.Printf("缓存大小: %d\n", cache.Size()) // 输出: 缓存大小: 3
	
	cache.Put("d", 4) // 这会移除最旧的项 "a"
	fmt.Printf("添加'd'后缓存大小: %d\n", cache.Size()) // 输出: 添加'd'后缓存大小: 3
	
	if value, exists := cache.Get("a"); exists {
		fmt.Printf("获取'a': %d\n", value)
	} else {
		fmt.Println("'a'已被移除") // 输出: 'a'已被移除
	}
	
	if value, exists := cache.Get("d"); exists {
		fmt.Printf("获取'd': %d\n", value) // 输出: 获取'd': 4
	}
	
	// 11. 泛型最佳实践
	fmt.Println("\n--- 泛型最佳实践 ---")
	
	fmt.Println("Go泛型最佳实践:")
	fmt.Println("1. 优先使用接口而不是泛型")
	fmt.Println("2. 泛型适用于数据结构和算法")
	fmt.Println("3. 使用有意义的类型参数名")
	fmt.Println("4. 合理使用类型约束")
	fmt.Println("5. 避免过度泛型化")
	fmt.Println("6. 考虑编译时间和复杂性")
	fmt.Println("7. 为泛型类型提供清晰的文档")
	fmt.Println("8. 测试不同类型参数的组合")
}

/*
运行命令: conda activate golang && go run 09_advanced/02_generics.go

Go语言泛型总结：

1. 泛型基础：
   - Go 1.18引入的特性
   - 类型参数：[T any]
   - 类型约束：interface定义
   - 类型推断：自动推断类型

2. 类型参数：
   - [T any]：任意类型
   - [T comparable]：可比较类型
   - [T Constraint]：自定义约束
   - 多类型参数：[T, U any]

3. 类型约束：
   - any：任意类型
   - comparable：可比较类型
   - 自定义约束：interface定义
   - 类型联合：T1 | T2 | T3

4. 泛型函数：
   - 函数级别的类型参数
   - 类型推断
   - 约束检查
   - 实例化

5. 泛型类型：
   - 结构体泛型
   - 接口泛型
   - 方法泛型
   - 类型别名

6. 应用场景：
   - 数据结构：栈、队列、树
   - 算法：排序、搜索、过滤
   - 容器：映射、缓存、集合
   - 工具函数：转换、验证

7. 最佳实践：
   - 优先使用接口
   - 合理使用约束
   - 避免过度泛型化
   - 提供清晰文档
   - 充分测试

8. 注意事项：
   - 编译时间可能增加
   - 代码复杂性增加
   - 类型推断限制
   - 向后兼容性
*/
