// 01_reflection.go - Go语言反射
// 这个文件演示了Go语言中反射的概念和使用

package main

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// 1. 基本反射示例
func basicReflectionDemo() {
	fmt.Println("--- 基本反射示例 ---")
	
	// 不同类型的变量
	var i int = 42
	var s string = "Hello"
	var f float64 = 3.14
	var b bool = true
	
	values := []interface{}{i, s, f, b}
	
	for _, v := range values {
		// 获取反射对象
		rv := reflect.ValueOf(v)
		rt := reflect.TypeOf(v)
		
		fmt.Printf("值: %v, 类型: %v, 种类: %v\n", 
			rv.Interface(), rt, rv.Kind())
	}
	// 输出:
	// 值: 42, 类型: int, 种类: int
	// 值: Hello, 类型: string, 种类: string
	// 值: 3.14, 类型: float64, 种类: float64
	// 值: true, 类型: bool, 种类: bool
}

// 2. 结构体反射
type Person struct {
	Name    string `json:"name" validate:"required"`
	Age     int    `json:"age" validate:"min=0,max=150"`
	Email   string `json:"email" validate:"email"`
	private string // 私有字段
}

func (p Person) GetInfo() string {
	return fmt.Sprintf("%s (%d years old)", p.Name, p.Age)
}

func (p *Person) SetName(name string) {
	p.Name = name
}

func structReflectionDemo() {
	fmt.Println("\n--- 结构体反射 ---")
	
	person := Person{
		Name:    "张三",
		Age:     25,
		Email:   "<EMAIL>",
		private: "私有数据",
	}
	
	rt := reflect.TypeOf(person)
	rv := reflect.ValueOf(person)
	
	fmt.Printf("类型名: %s\n", rt.Name())
	fmt.Printf("包路径: %s\n", rt.PkgPath())
	fmt.Printf("字段数量: %d\n", rt.NumField())
	
	// 遍历字段
	fmt.Println("\n字段信息:")
	for i := 0; i < rt.NumField(); i++ {
		field := rt.Field(i)
		value := rv.Field(i)
		
		fmt.Printf("  字段 %d:\n", i)
		fmt.Printf("    名称: %s\n", field.Name)
		fmt.Printf("    类型: %s\n", field.Type)
		fmt.Printf("    标签: %s\n", field.Tag)
		fmt.Printf("    可导出: %t\n", field.IsExported())
		
		if value.CanInterface() {
			fmt.Printf("    值: %v\n", value.Interface())
		} else {
			fmt.Printf("    值: <不可访问>\n")
		}
		
		// 解析标签
		if jsonTag := field.Tag.Get("json"); jsonTag != "" {
			fmt.Printf("    JSON标签: %s\n", jsonTag)
		}
		if validateTag := field.Tag.Get("validate"); validateTag != "" {
			fmt.Printf("    验证标签: %s\n", validateTag)
		}
	}
	
	// 遍历方法
	fmt.Printf("\n方法数量: %d\n", rt.NumMethod())
	for i := 0; i < rt.NumMethod(); i++ {
		method := rt.Method(i)
		fmt.Printf("  方法 %d: %s, 类型: %s\n", i, method.Name, method.Type)
	}
}

// 3. 反射修改值
func reflectionModifyDemo() {
	fmt.Println("\n--- 反射修改值 ---")
	
	// 修改基本类型
	var x int = 10
	fmt.Printf("修改前: x = %d\n", x)
	
	rv := reflect.ValueOf(&x) // 必须传递指针
	if rv.Kind() == reflect.Ptr && rv.Elem().CanSet() {
		rv.Elem().SetInt(20)
	}
	fmt.Printf("修改后: x = %d\n", x) // 输出: 修改后: x = 20
	
	// 修改结构体字段
	person := Person{Name: "原名", Age: 20}
	fmt.Printf("修改前: %+v\n", person)
	
	rv = reflect.ValueOf(&person).Elem()
	if nameField := rv.FieldByName("Name"); nameField.CanSet() {
		nameField.SetString("新名字")
	}
	if ageField := rv.FieldByName("Age"); ageField.CanSet() {
		ageField.SetInt(30)
	}
	
	fmt.Printf("修改后: %+v\n", person) // 输出: 修改后: {Name:新名字 Age:30 Email: private:}
	
	// 修改切片
	slice := []int{1, 2, 3}
	fmt.Printf("修改前切片: %v\n", slice)
	
	rv = reflect.ValueOf(&slice).Elem()
	if rv.Kind() == reflect.Slice {
		// 修改现有元素
		if rv.Len() > 0 {
			rv.Index(0).SetInt(10)
		}
		
		// 添加新元素
		newElem := reflect.ValueOf(4)
		rv.Set(reflect.Append(rv, newElem))
	}
	
	fmt.Printf("修改后切片: %v\n", slice) // 输出: 修改后切片: [10 2 3 4]
}

// 4. 动态调用方法
func methodCallDemo() {
	fmt.Println("\n--- 动态调用方法 ---")
	
	person := Person{Name: "李四", Age: 28}
	
	rv := reflect.ValueOf(person)
	
	// 调用无参数方法
	if method := rv.MethodByName("GetInfo"); method.IsValid() {
		results := method.Call(nil)
		fmt.Printf("GetInfo结果: %s\n", results[0].String())
	}
	
	// 调用有参数的方法（需要指针接收者）
	rv = reflect.ValueOf(&person)
	if method := rv.MethodByName("SetName"); method.IsValid() {
		args := []reflect.Value{reflect.ValueOf("王五")}
		method.Call(args)
		fmt.Printf("SetName后: %s\n", person.Name) // 输出: SetName后: 王五
	}
}

// 5. 类型断言和类型开关的反射版本
func typeAssertionReflectionDemo() {
	fmt.Println("\n--- 类型断言的反射版本 ---")
	
	values := []interface{}{
		42,
		"hello",
		3.14,
		[]int{1, 2, 3},
		Person{Name: "测试", Age: 25},
	}
	
	for i, v := range values {
		rv := reflect.ValueOf(v)
		rt := reflect.TypeOf(v)
		
		fmt.Printf("值 %d:\n", i)
		fmt.Printf("  类型: %s\n", rt)
		fmt.Printf("  种类: %s\n", rv.Kind())
		
		// 根据种类进行不同处理
		switch rv.Kind() {
		case reflect.Int:
			fmt.Printf("  整数值: %d\n", rv.Int())
		case reflect.String:
			fmt.Printf("  字符串值: %s\n", rv.String())
		case reflect.Float64:
			fmt.Printf("  浮点值: %.2f\n", rv.Float())
		case reflect.Slice:
			fmt.Printf("  切片长度: %d\n", rv.Len())
			if rv.Len() > 0 {
				fmt.Printf("  第一个元素: %v\n", rv.Index(0).Interface())
			}
		case reflect.Struct:
			fmt.Printf("  结构体字段数: %d\n", rv.NumField())
		}
		fmt.Println()
	}
}

// 6. 创建新类型实例
func createInstanceDemo() {
	fmt.Println("\n--- 创建新类型实例 ---")
	
	// 创建基本类型
	intType := reflect.TypeOf(int(0))
	intValue := reflect.New(intType).Elem()
	intValue.SetInt(100)
	fmt.Printf("创建的int: %v\n", intValue.Interface())
	
	// 创建切片
	sliceType := reflect.SliceOf(reflect.TypeOf(string("")))
	sliceValue := reflect.MakeSlice(sliceType, 0, 3)
	sliceValue = reflect.Append(sliceValue, reflect.ValueOf("hello"))
	sliceValue = reflect.Append(sliceValue, reflect.ValueOf("world"))
	fmt.Printf("创建的切片: %v\n", sliceValue.Interface())
	
	// 创建映射
	mapType := reflect.MapOf(reflect.TypeOf(string("")), reflect.TypeOf(int(0)))
	mapValue := reflect.MakeMap(mapType)
	mapValue.SetMapIndex(reflect.ValueOf("key1"), reflect.ValueOf(10))
	mapValue.SetMapIndex(reflect.ValueOf("key2"), reflect.ValueOf(20))
	fmt.Printf("创建的映射: %v\n", mapValue.Interface())
	
	// 创建结构体
	personType := reflect.TypeOf(Person{})
	personValue := reflect.New(personType).Elem()
	
	// 设置字段值
	personValue.FieldByName("Name").SetString("反射创建")
	personValue.FieldByName("Age").SetInt(30)
	personValue.FieldByName("Email").SetString("<EMAIL>")
	
	fmt.Printf("创建的结构体: %+v\n", personValue.Interface())
}

// 7. 实际应用：JSON序列化器
func jsonSerializerDemo() {
	fmt.Println("\n--- 实际应用：简单JSON序列化器 ---")
	
	// 简单的JSON序列化函数
	toJSON := func(v interface{}) string {
		rv := reflect.ValueOf(v)
		rt := reflect.TypeOf(v)
		
		switch rv.Kind() {
		case reflect.String:
			return fmt.Sprintf(`"%s"`, rv.String())
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return strconv.FormatInt(rv.Int(), 10)
		case reflect.Float32, reflect.Float64:
			return strconv.FormatFloat(rv.Float(), 'f', -1, 64)
		case reflect.Bool:
			return strconv.FormatBool(rv.Bool())
		case reflect.Struct:
			var fields []string
			for i := 0; i < rv.NumField(); i++ {
				field := rt.Field(i)
				if !field.IsExported() {
					continue
				}
				
				fieldValue := rv.Field(i)
				jsonName := field.Name
				
				// 检查json标签
				if tag := field.Tag.Get("json"); tag != "" && tag != "-" {
					if commaIdx := strings.Index(tag, ","); commaIdx != -1 {
						jsonName = tag[:commaIdx]
					} else {
						jsonName = tag
					}
				}
				
				fieldJSON := toJSON(fieldValue.Interface())
				fields = append(fields, fmt.Sprintf(`"%s":%s`, jsonName, fieldJSON))
			}
			return "{" + strings.Join(fields, ",") + "}"
		default:
			return `null`
		}
	}
	
	// 测试序列化
	person := Person{
		Name:  "张三",
		Age:   25,
		Email: "<EMAIL>",
	}
	
	jsonStr := toJSON(person)
	fmt.Printf("序列化结果: %s\n", jsonStr)
}

// 8. 实际应用：结构体验证器
func validatorDemo() {
	fmt.Println("\n--- 实际应用：结构体验证器 ---")
	
	// 简单的验证函数
	validate := func(v interface{}) []string {
		var errors []string
		
		rv := reflect.ValueOf(v)
		rt := reflect.TypeOf(v)
		
		if rv.Kind() != reflect.Struct {
			return []string{"只能验证结构体"}
		}
		
		for i := 0; i < rv.NumField(); i++ {
			field := rt.Field(i)
			fieldValue := rv.Field(i)
			
			validateTag := field.Tag.Get("validate")
			if validateTag == "" {
				continue
			}
			
			rules := strings.Split(validateTag, ",")
			for _, rule := range rules {
				rule = strings.TrimSpace(rule)
				
				switch {
				case rule == "required":
					if isZeroValue(fieldValue) {
						errors = append(errors, fmt.Sprintf("字段 %s 是必需的", field.Name))
					}
				case strings.HasPrefix(rule, "min="):
					if minStr := strings.TrimPrefix(rule, "min="); minStr != "" {
						if min, err := strconv.Atoi(minStr); err == nil {
							if fieldValue.Kind() == reflect.Int && fieldValue.Int() < int64(min) {
								errors = append(errors, fmt.Sprintf("字段 %s 的值不能小于 %d", field.Name, min))
							}
						}
					}
				case strings.HasPrefix(rule, "max="):
					if maxStr := strings.TrimPrefix(rule, "max="); maxStr != "" {
						if max, err := strconv.Atoi(maxStr); err == nil {
							if fieldValue.Kind() == reflect.Int && fieldValue.Int() > int64(max) {
								errors = append(errors, fmt.Sprintf("字段 %s 的值不能大于 %d", field.Name, max))
							}
						}
					}
				case rule == "email":
					if fieldValue.Kind() == reflect.String {
						email := fieldValue.String()
						if !strings.Contains(email, "@") {
							errors = append(errors, fmt.Sprintf("字段 %s 不是有效的邮箱地址", field.Name))
						}
					}
				}
			}
		}
		
		return errors
	}
	
	isZeroValue := func(v reflect.Value) bool {
		switch v.Kind() {
		case reflect.String:
			return v.String() == ""
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return v.Int() == 0
		case reflect.Float32, reflect.Float64:
			return v.Float() == 0
		case reflect.Bool:
			return !v.Bool()
		default:
			return false
		}
	}
	
	// 测试验证
	testCases := []Person{
		{Name: "张三", Age: 25, Email: "<EMAIL>"},
		{Name: "", Age: 25, Email: "<EMAIL>"},
		{Name: "李四", Age: -5, Email: "invalid-email"},
		{Name: "王五", Age: 200, Email: "<EMAIL>"},
	}
	
	for i, person := range testCases {
		fmt.Printf("测试用例 %d: %+v\n", i+1, person)
		if errors := validate(person); len(errors) > 0 {
			fmt.Printf("  验证错误:\n")
			for _, err := range errors {
				fmt.Printf("    - %s\n", err)
			}
		} else {
			fmt.Printf("  验证通过\n")
		}
		fmt.Println()
	}
}

func main() {
	fmt.Println("=== Go语言反射示例 ===")
	
	// 1. 基本反射
	basicReflectionDemo()
	
	// 2. 结构体反射
	structReflectionDemo()
	
	// 3. 反射修改值
	reflectionModifyDemo()
	
	// 4. 动态调用方法
	methodCallDemo()
	
	// 5. 类型断言的反射版本
	typeAssertionReflectionDemo()
	
	// 6. 创建新类型实例
	createInstanceDemo()
	
	// 7. 实际应用：JSON序列化器
	jsonSerializerDemo()
	
	// 8. 实际应用：结构体验证器
	validatorDemo()
	
	// 9. 反射最佳实践
	fmt.Println("\n--- 反射最佳实践 ---")
	
	fmt.Println("反射使用原则:")
	fmt.Println("1. 反射会降低性能，谨慎使用")
	fmt.Println("2. 反射代码难以理解和维护")
	fmt.Println("3. 编译时无法检查类型安全")
	fmt.Println("4. 优先使用接口而不是反射")
	fmt.Println("5. 在库和框架中使用反射提供通用性")
	fmt.Println("6. 使用反射时要进行充分的错误检查")
	fmt.Println("7. 文档化反射代码的行为")
	fmt.Println("8. 考虑使用代码生成替代反射")
}

/*
运行命令: conda activate golang && go run 09_advanced/01_reflection.go

Go语言反射总结：

1. 反射基础：
   - reflect.TypeOf(): 获取类型信息
   - reflect.ValueOf(): 获取值信息
   - Kind(): 获取底层类型种类
   - Interface(): 获取原始值

2. 类型信息：
   - Name(): 类型名称
   - PkgPath(): 包路径
   - NumField(): 字段数量
   - Field(): 获取字段信息
   - NumMethod(): 方法数量
   - Method(): 获取方法信息

3. 值操作：
   - CanSet(): 是否可设置
   - Set*(): 设置值
   - Index(): 访问数组/切片元素
   - MapIndex(): 访问映射元素
   - Call(): 调用方法

4. 创建实例：
   - reflect.New(): 创建指针
   - reflect.MakeSlice(): 创建切片
   - reflect.MakeMap(): 创建映射
   - reflect.MakeChan(): 创建通道

5. 应用场景：
   - 序列化/反序列化
   - 数据验证
   - ORM框架
   - 依赖注入
   - 通用算法

6. 性能考虑：
   - 反射比直接调用慢10-100倍
   - 避免在热点代码中使用
   - 缓存反射结果
   - 考虑代码生成替代

7. 最佳实践：
   - 谨慎使用反射
   - 充分的错误检查
   - 优先使用接口
   - 文档化反射代码
   - 考虑类型安全

8. 注意事项：
   - 编译时无法检查
   - 代码难以理解
   - 性能开销较大
   - 运行时错误风险
*/
