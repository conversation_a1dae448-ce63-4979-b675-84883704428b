# Go语言安装指南

这个文档将指导你在macOS上安装Go语言并设置开发环境。

## 方法一：使用官方安装包（推荐）

### 1. 下载Go安装包

访问Go官方网站：https://golang.org/dl/

下载适合macOS的安装包（通常是 `.pkg` 文件）

### 2. 安装Go

双击下载的 `.pkg` 文件，按照安装向导完成安装。

默认安装路径：`/usr/local/go`

### 3. 设置环境变量

打开终端，编辑你的shell配置文件：

```bash
# 如果使用bash
nano ~/.bash_profile

# 如果使用zsh（macOS Catalina及以后的默认shell）
nano ~/.zshrc
```

添加以下内容：

```bash
# Go环境变量
export GOROOT=/usr/local/go
export GOPATH=$HOME/go
export PATH=$GOROOT/bin:$GOPATH/bin:$PATH
```

保存文件后，重新加载配置：

```bash
# bash用户
source ~/.bash_profile

# zsh用户
source ~/.zshrc
```

## 方法二：使用Homebrew

### 1. 安装Homebrew（如果还没有安装）

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### 2. 使用Homebrew安装Go

```bash
brew install go
```

### 3. 设置环境变量

Homebrew会自动设置大部分环境变量，但你可能需要设置GOPATH：

```bash
# 编辑shell配置文件
nano ~/.zshrc  # 或 ~/.bash_profile

# 添加以下内容
export GOPATH=$HOME/go
export PATH=$GOPATH/bin:$PATH
```

重新加载配置：

```bash
source ~/.zshrc  # 或 source ~/.bash_profile
```

## 方法三：使用Conda（适合已有conda环境的用户）

### 1. 创建Go专用的conda环境

```bash
# 创建名为golang的环境
conda create -n golang

# 激活环境
conda activate golang
```

### 2. 在conda环境中安装Go

```bash
conda install -c conda-forge go
```

### 3. 验证安装

```bash
go version
```

## 验证安装

无论使用哪种方法，安装完成后都应该验证：

```bash
# 检查Go版本
go version

# 检查Go环境信息
go env

# 检查重要的环境变量
echo $GOROOT
echo $GOPATH
echo $PATH
```

预期输出示例：
```
$ go version
go version go1.21.0 darwin/amd64

$ go env GOROOT
/usr/local/go

$ go env GOPATH
/Users/<USER>/go
```

## 设置工作空间

### 1. 创建Go工作空间目录

```bash
mkdir -p $GOPATH/src
mkdir -p $GOPATH/bin
mkdir -p $GOPATH/pkg
```

### 2. 测试安装

创建一个简单的测试程序：

```bash
# 进入你的学习项目目录
cd /Users/<USER>/dev/GolangLearning

# 运行第一个示例
go run 01_basics/01_hello_world.go
```

如果看到 "Hello, World!" 输出，说明安装成功！

## IDE和编辑器推荐

### 1. Visual Studio Code（推荐）

- 安装VS Code
- 安装Go扩展（Go Team at Google）
- 扩展会自动提示安装Go工具

### 2. GoLand（JetBrains）

- 专业的Go IDE
- 功能强大但需要付费

### 3. Vim/Neovim

- 安装vim-go插件
- 适合喜欢命令行的开发者

## 常用Go工具安装

安装完Go后，可以安装一些常用工具：

```bash
# 代码格式化工具（通常已包含）
go install golang.org/x/tools/cmd/goimports@latest

# 代码检查工具
go install golang.org/x/lint/golint@latest

# 调试工具
go install github.com/go-delve/delve/cmd/dlv@latest
```

## 故障排除

### 问题1：command not found: go

**解决方案：**
- 检查PATH环境变量是否包含Go的bin目录
- 重新加载shell配置文件
- 重启终端

### 问题2：GOPATH未设置

**解决方案：**
```bash
export GOPATH=$HOME/go
export PATH=$GOPATH/bin:$PATH
```

### 问题3：权限问题

**解决方案：**
```bash
# 确保GOPATH目录有正确的权限
chmod -R 755 $GOPATH
```

### 问题4：conda环境问题

**解决方案：**
```bash
# 初始化conda（如果还没有）
conda init

# 重启终端后激活环境
conda activate golang
```

## 运行学习项目

安装完成后，你可以运行项目中的任何示例：

```bash
# 进入项目目录
cd /Users/<USER>/dev/GolangLearning

# 如果使用conda环境，先激活
conda activate golang

# 运行示例（按学习顺序）
go run 01_basics/01_hello_world.go
go run 01_basics/02_variables.go
go run 01_basics/03_constants.go
# ... 等等

# 或者编译后运行
go build 01_basics/01_hello_world.go
./01_hello_world
```

## 下一步

安装完成后，建议按照README.md中的学习顺序开始学习Go语言。

祝你学习愉快！🚀
