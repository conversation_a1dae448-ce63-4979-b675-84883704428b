# Golang 学习项目

这是一个系统化的Golang学习项目，包含了从基础到高级的各种语法和功能示例。

## 学习顺序

按照以下顺序学习，每个文件都包含详细的注释和预期输出：

### 第一阶段：基础语法 (1-3天)
1. `01_basics/01_hello_world.go` - Hello World 和基本程序结构
2. `01_basics/02_variables.go` - 变量声明和初始化
3. `01_basics/03_constants.go` - 常量定义
4. `01_basics/04_data_types.go` - 基本数据类型
5. `01_basics/05_operators.go` - 运算符

### 第二阶段：控制流程 (2-3天)
6. `02_control_flow/01_if_else.go` - 条件语句
7. `02_control_flow/02_loops.go` - 循环语句
8. `02_control_flow/03_switch.go` - Switch语句

### 第三阶段：函数 (2-3天)
9. `03_functions/01_basic_functions.go` - 基本函数
10. `03_functions/02_parameters.go` - 参数传递
11. `03_functions/03_return_values.go` - 返回值
12. `03_functions/04_anonymous_functions.go` - 匿名函数和闭包

### 第四阶段：数据结构 (3-4天)
13. `04_data_structures/01_arrays.go` - 数组
14. `04_data_structures/02_slices.go` - 切片
15. `04_data_structures/03_maps.go` - 映射
16. `04_data_structures/04_structs.go` - 结构体
17. `04_data_structures/05_pointers.go` - 指针

### 第五阶段：面向对象 (3-4天)
18. `05_oop/01_methods.go` - 方法
19. `05_oop/02_interfaces.go` - 接口
20. `05_oop/03_embedding.go` - 嵌入
21. `05_oop/04_polymorphism.go` - 多态

### 第六阶段：并发编程 (4-5天)
22. `06_concurrency/01_goroutines.go` - Goroutines
23. `06_concurrency/02_channels.go` - Channels
24. `06_concurrency/03_select.go` - Select语句
25. `06_concurrency/04_sync.go` - 同步原语

### 第七阶段：错误处理和包管理 (2-3天)
26. `07_error_handling/01_basic_errors.go` - 基本错误处理
27. `07_error_handling/02_custom_errors.go` - 自定义错误
28. `08_packages/01_package_basics.go` - 包基础
29. `08_packages/02_modules.go` - 模块管理

### 第八阶段：高级特性 (3-4天)
30. `09_advanced/01_reflection.go` - 反射
31. `09_advanced/02_generics.go` - 泛型 (Go 1.18+)
32. `09_advanced/03_file_operations.go` - 文件操作
33. `09_advanced/04_json.go` - JSON处理
34. `09_advanced/05_http_client.go` - HTTP客户端

### 第九阶段：实践项目 (5-7天)
35. `10_projects/01_calculator.go` - 计算器
36. `10_projects/02_todo_app.go` - 待办事项应用
37. `10_projects/03_web_server.go` - 简单Web服务器

## 如何运行

确保你已经安装了Go并激活了golang conda环境：

```bash
# 激活conda环境
conda activate golang

# 运行单个文件
go run 01_basics/01_hello_world.go

# 或者编译后运行
go build 01_basics/01_hello_world.go
./01_hello_world
```

## 学习建议

1. **按顺序学习**：每个文件都建立在前面的知识基础上
2. **动手实践**：不要只看代码，一定要运行每个示例
3. **修改实验**：尝试修改代码，观察结果变化
4. **做笔记**：记录重要概念和容易混淆的地方
5. **多练习**：完成每个阶段后，尝试写一些小程序巩固知识

## 预计学习时间

- **总时间**：25-35天（每天1-2小时）
- **基础掌握**：10-15天
- **进阶应用**：15-20天
- **项目实践**：5-10天

祝你学习愉快！🚀
