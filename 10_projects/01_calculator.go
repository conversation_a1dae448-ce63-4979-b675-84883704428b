// 01_calculator.go - 命令行计算器项目
// 这是一个完整的命令行计算器，支持基本运算、历史记录和变量存储

package main

import (
	"bufio"
	"fmt"
	"math"
	"os"
	"strconv"
	"strings"
)

// 计算器结构体
type Calculator struct {
	history   []string               // 计算历史
	variables map[string]float64     // 变量存储
	memory    float64                // 内存值
}

// 创建新的计算器实例
func NewCalculator() *Calculator {
	return &Calculator{
		history:   make([]string, 0),
		variables: make(map[string]float64),
		memory:    0,
	}
}

// 添加到历史记录
func (c *Calculator) addToHistory(expression string, result float64) {
	record := fmt.Sprintf("%s = %.6g", expression, result)
	c.history = append(c.history, record)
	
	// 限制历史记录数量
	if len(c.history) > 50 {
		c.history = c.history[1:]
	}
}

// 显示历史记录
func (c *Calculator) showHistory() {
	if len(c.history) == 0 {
		fmt.Println("没有计算历史")
		return
	}
	
	fmt.Println("计算历史:")
	for i, record := range c.history {
		fmt.Printf("%2d: %s\n", i+1, record)
	}
}

// 清除历史记录
func (c *Calculator) clearHistory() {
	c.history = make([]string, 0)
	fmt.Println("历史记录已清除")
}

// 设置变量
func (c *Calculator) setVariable(name string, value float64) {
	c.variables[name] = value
	fmt.Printf("变量 %s = %.6g\n", name, value)
}

// 获取变量值
func (c *Calculator) getVariable(name string) (float64, bool) {
	value, exists := c.variables[name]
	return value, exists
}

// 显示所有变量
func (c *Calculator) showVariables() {
	if len(c.variables) == 0 {
		fmt.Println("没有定义变量")
		return
	}
	
	fmt.Println("已定义的变量:")
	for name, value := range c.variables {
		fmt.Printf("  %s = %.6g\n", name, value)
	}
}

// 基本运算函数
func (c *Calculator) add(a, b float64) float64 {
	return a + b
}

func (c *Calculator) subtract(a, b float64) float64 {
	return a - b
}

func (c *Calculator) multiply(a, b float64) float64 {
	return a * b
}

func (c *Calculator) divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}
	return a / b, nil
}

func (c *Calculator) power(a, b float64) float64 {
	return math.Pow(a, b)
}

func (c *Calculator) sqrt(a float64) (float64, error) {
	if a < 0 {
		return 0, fmt.Errorf("不能计算负数的平方根")
	}
	return math.Sqrt(a), nil
}

// 解析数字或变量
func (c *Calculator) parseValue(token string) (float64, error) {
	// 特殊值处理
	switch strings.ToLower(token) {
	case "pi":
		return math.Pi, nil
	case "e":
		return math.E, nil
	case "m", "memory":
		return c.memory, nil
	}
	
	// 尝试解析为数字
	if value, err := strconv.ParseFloat(token, 64); err == nil {
		return value, nil
	}
	
	// 尝试从变量中获取
	if value, exists := c.getVariable(token); exists {
		return value, nil
	}
	
	return 0, fmt.Errorf("无法识别的值: %s", token)
}

// 简单表达式计算
func (c *Calculator) evaluateSimpleExpression(expression string) (float64, error) {
	tokens := strings.Fields(expression)
	
	if len(tokens) == 1 {
		// 单个值
		return c.parseValue(tokens[0])
	}
	
	if len(tokens) == 2 {
		// 单元运算 (如 sqrt 9)
		operator := strings.ToLower(tokens[0])
		operand, err := c.parseValue(tokens[1])
		if err != nil {
			return 0, err
		}
		
		switch operator {
		case "sqrt":
			return c.sqrt(operand)
		case "sin":
			return math.Sin(operand), nil
		case "cos":
			return math.Cos(operand), nil
		case "tan":
			return math.Tan(operand), nil
		case "log":
			if operand <= 0 {
				return 0, fmt.Errorf("对数的参数必须大于0")
			}
			return math.Log10(operand), nil
		case "ln":
			if operand <= 0 {
				return 0, fmt.Errorf("自然对数的参数必须大于0")
			}
			return math.Log(operand), nil
		default:
			return 0, fmt.Errorf("未知的单元运算符: %s", operator)
		}
	}
	
	if len(tokens) == 3 {
		// 二元运算 (如 5 + 3)
		left, err := c.parseValue(tokens[0])
		if err != nil {
			return 0, err
		}
		
		operator := tokens[1]
		
		right, err := c.parseValue(tokens[2])
		if err != nil {
			return 0, err
		}
		
		switch operator {
		case "+":
			return c.add(left, right), nil
		case "-":
			return c.subtract(left, right), nil
		case "*", "×":
			return c.multiply(left, right), nil
		case "/", "÷":
			return c.divide(left, right)
		case "^", "**":
			return c.power(left, right), nil
		case "%":
			if right == 0 {
				return 0, fmt.Errorf("模运算的除数不能为零")
			}
			return math.Mod(left, right), nil
		default:
			return 0, fmt.Errorf("未知的运算符: %s", operator)
		}
	}
	
	return 0, fmt.Errorf("无法解析表达式: %s", expression)
}

// 处理特殊命令
func (c *Calculator) handleCommand(input string) bool {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return false
	}
	
	command := strings.ToLower(parts[0])
	
	switch command {
	case "help", "h":
		c.showHelp()
		return true
		
	case "history", "hist":
		c.showHistory()
		return true
		
	case "clear", "cls":
		c.clearHistory()
		return true
		
	case "variables", "vars":
		c.showVariables()
		return true
		
	case "set":
		if len(parts) == 3 {
			if value, err := strconv.ParseFloat(parts[2], 64); err == nil {
				c.setVariable(parts[1], value)
			} else {
				fmt.Printf("无效的数值: %s\n", parts[2])
			}
		} else {
			fmt.Println("用法: set <变量名> <值>")
		}
		return true
		
	case "memory", "m":
		if len(parts) == 1 {
			fmt.Printf("内存值: %.6g\n", c.memory)
		} else if len(parts) == 2 {
			switch strings.ToLower(parts[1]) {
			case "clear", "c":
				c.memory = 0
				fmt.Println("内存已清除")
			default:
				if value, err := strconv.ParseFloat(parts[1], 64); err == nil {
					c.memory = value
					fmt.Printf("内存设置为: %.6g\n", c.memory)
				} else {
					fmt.Printf("无效的数值: %s\n", parts[1])
				}
			}
		}
		return true
		
	case "exit", "quit", "q":
		fmt.Println("感谢使用计算器！")
		return true
		
	default:
		return false
	}
}

// 显示帮助信息
func (c *Calculator) showHelp() {
	fmt.Println(`
=== 计算器帮助 ===

基本运算:
  5 + 3        加法
  10 - 4       减法
  6 * 7        乘法
  15 / 3       除法
  2 ^ 3        幂运算
  17 % 5       模运算

函数:
  sqrt 16      平方根
  sin 1.57     正弦
  cos 0        余弦
  tan 0.785    正切
  log 100      常用对数
  ln 2.718     自然对数

常量:
  pi           圆周率
  e            自然常数

变量:
  set x 10     设置变量 x = 10
  x + 5        使用变量进行计算
  variables    显示所有变量

内存:
  memory 25    设置内存值
  m + 10       使用内存值计算
  memory clear 清除内存

命令:
  help         显示帮助
  history      显示计算历史
  clear        清除历史记录
  exit         退出程序

示例:
  > 5 + 3
  8
  > set radius 5
  > pi * radius ^ 2
  78.5398
`)
}

// 主计算循环
func (c *Calculator) run() {
	fmt.Println("=== Go语言命令行计算器 ===")
	fmt.Println("输入 'help' 查看帮助，输入 'exit' 退出")
	fmt.Println()
	
	scanner := bufio.NewScanner(os.Stdin)
	
	for {
		fmt.Print("计算器> ")
		
		if !scanner.Scan() {
			break
		}
		
		input := strings.TrimSpace(scanner.Text())
		
		if input == "" {
			continue
		}
		
		// 处理特殊命令
		if c.handleCommand(input) {
			if strings.HasPrefix(strings.ToLower(input), "exit") ||
			   strings.HasPrefix(strings.ToLower(input), "quit") ||
			   strings.HasPrefix(strings.ToLower(input), "q") {
				break
			}
			continue
		}
		
		// 处理赋值操作 (如 x = 5 + 3)
		if strings.Contains(input, "=") && !strings.Contains(input, "==") {
			parts := strings.SplitN(input, "=", 2)
			if len(parts) == 2 {
				varName := strings.TrimSpace(parts[0])
				expression := strings.TrimSpace(parts[1])
				
				if result, err := c.evaluateSimpleExpression(expression); err == nil {
					c.setVariable(varName, result)
					c.addToHistory(input, result)
				} else {
					fmt.Printf("错误: %v\n", err)
				}
				continue
			}
		}
		
		// 处理内存操作 (如 m+ 5, m- 3)
		if strings.HasPrefix(input, "m+") || strings.HasPrefix(input, "m-") {
			operator := input[:2]
			valueStr := strings.TrimSpace(input[2:])
			
			if value, err := c.parseValue(valueStr); err == nil {
				if operator == "m+" {
					c.memory += value
				} else {
					c.memory -= value
				}
				fmt.Printf("内存值: %.6g\n", c.memory)
			} else {
				fmt.Printf("错误: %v\n", err)
			}
			continue
		}
		
		// 计算表达式
		if result, err := c.evaluateSimpleExpression(input); err == nil {
			fmt.Printf("%.6g\n", result)
			c.addToHistory(input, result)
		} else {
			fmt.Printf("错误: %v\n", err)
		}
	}
	
	if err := scanner.Err(); err != nil {
		fmt.Printf("读取输入时出错: %v\n", err)
	}
}

func main() {
	calculator := NewCalculator()
	calculator.run()
}

/*
运行命令: conda activate golang && go run 10_projects/01_calculator.go

计算器项目特性：

1. 基本运算：
   - 加法、减法、乘法、除法
   - 幂运算、模运算
   - 支持小数和负数

2. 数学函数：
   - 平方根、三角函数
   - 对数函数
   - 数学常量(π, e)

3. 变量系统：
   - 定义和使用变量
   - 变量赋值和计算
   - 变量列表显示

4. 内存功能：
   - 内存存储和读取
   - 内存加减操作
   - 内存清除

5. 历史记录：
   - 计算历史保存
   - 历史记录查看
   - 历史记录清除

6. 交互界面：
   - 命令行交互
   - 帮助系统
   - 错误处理

7. 扩展功能：
   - 支持多种输入格式
   - 灵活的表达式解析
   - 用户友好的提示

使用示例：
> 5 + 3
8
> set radius 5
变量 radius = 5
> pi * radius ^ 2
78.5398
> sqrt 16
4
> history
计算历史:
 1: 5 + 3 = 8
 2: pi * radius ^ 2 = 78.5398
 3: sqrt 16 = 4

这个项目展示了：
- 结构体和方法的使用
- 字符串处理和解析
- 错误处理机制
- 用户交互设计
- 数据存储和管理
- 命令模式的实现
*/
