// 02_todo_app.go - 待办事项管理应用
// 这是一个完整的命令行待办事项管理应用，支持任务的增删改查、优先级、状态管理等

package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 任务优先级
type Priority int

const (
	Low Priority = iota
	Medium
	High
	Urgent
)

func (p Priority) String() string {
	switch p {
	case Low:
		return "低"
	case Medium:
		return "中"
	case High:
		return "高"
	case Urgent:
		return "紧急"
	default:
		return "未知"
	}
}

// 任务状态
type Status int

const (
	Pending Status = iota
	InProgress
	Completed
	Cancelled
)

func (s Status) String() string {
	switch s {
	case Pending:
		return "待办"
	case InProgress:
		return "进行中"
	case Completed:
		return "已完成"
	case Cancelled:
		return "已取消"
	default:
		return "未知"
	}
}

// 任务结构体
type Task struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Priority    Priority  `json:"priority"`
	Status      Status    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DueDate     *time.Time `json:"due_date,omitempty"`
	Tags        []string  `json:"tags,omitempty"`
}

// 待办事项管理器
type TodoManager struct {
	tasks    []Task
	nextID   int
	filename string
}

// 创建新的待办事项管理器
func NewTodoManager(filename string) *TodoManager {
	tm := &TodoManager{
		tasks:    make([]Task, 0),
		nextID:   1,
		filename: filename,
	}
	tm.loadFromFile()
	return tm
}

// 从文件加载任务
func (tm *TodoManager) loadFromFile() error {
	if _, err := os.Stat(tm.filename); os.IsNotExist(err) {
		return nil // 文件不存在，返回空列表
	}
	
	data, err := os.ReadFile(tm.filename)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}
	
	if len(data) == 0 {
		return nil // 空文件
	}
	
	err = json.Unmarshal(data, &tm.tasks)
	if err != nil {
		return fmt.Errorf("解析JSON失败: %v", err)
	}
	
	// 更新nextID
	for _, task := range tm.tasks {
		if task.ID >= tm.nextID {
			tm.nextID = task.ID + 1
		}
	}
	
	fmt.Printf("从文件加载了 %d 个任务\n", len(tm.tasks))
	return nil
}

// 保存任务到文件
func (tm *TodoManager) saveToFile() error {
	data, err := json.MarshalIndent(tm.tasks, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}
	
	err = os.WriteFile(tm.filename, data, 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}
	
	return nil
}

// 添加任务
func (tm *TodoManager) addTask(title, description string, priority Priority, tags []string) {
	task := Task{
		ID:          tm.nextID,
		Title:       title,
		Description: description,
		Priority:    priority,
		Status:      Pending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Tags:        tags,
	}
	
	tm.tasks = append(tm.tasks, task)
	tm.nextID++
	
	fmt.Printf("任务已添加 (ID: %d): %s\n", task.ID, task.Title)
	tm.saveToFile()
}

// 根据ID查找任务
func (tm *TodoManager) findTaskByID(id int) (*Task, int) {
	for i, task := range tm.tasks {
		if task.ID == id {
			return &tm.tasks[i], i
		}
	}
	return nil, -1
}

// 删除任务
func (tm *TodoManager) deleteTask(id int) {
	task, index := tm.findTaskByID(id)
	if task == nil {
		fmt.Printf("未找到ID为 %d 的任务\n", id)
		return
	}
	
	fmt.Printf("确认删除任务: %s (y/N): ", task.Title)
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Scan()
	response := strings.ToLower(strings.TrimSpace(scanner.Text()))
	
	if response == "y" || response == "yes" {
		tm.tasks = append(tm.tasks[:index], tm.tasks[index+1:]...)
		fmt.Printf("任务已删除: %s\n", task.Title)
		tm.saveToFile()
	} else {
		fmt.Println("删除操作已取消")
	}
}

// 更新任务状态
func (tm *TodoManager) updateTaskStatus(id int, status Status) {
	task, _ := tm.findTaskByID(id)
	if task == nil {
		fmt.Printf("未找到ID为 %d 的任务\n", id)
		return
	}
	
	oldStatus := task.Status
	task.Status = status
	task.UpdatedAt = time.Now()
	
	fmt.Printf("任务状态已更新: %s (%s -> %s)\n", task.Title, oldStatus, status)
	tm.saveToFile()
}

// 设置任务截止日期
func (tm *TodoManager) setTaskDueDate(id int, dueDate time.Time) {
	task, _ := tm.findTaskByID(id)
	if task == nil {
		fmt.Printf("未找到ID为 %d 的任务\n", id)
		return
	}
	
	task.DueDate = &dueDate
	task.UpdatedAt = time.Now()
	
	fmt.Printf("任务截止日期已设置: %s -> %s\n", task.Title, dueDate.Format("2006-01-02"))
	tm.saveToFile()
}

// 更新任务优先级
func (tm *TodoManager) updateTaskPriority(id int, priority Priority) {
	task, _ := tm.findTaskByID(id)
	if task == nil {
		fmt.Printf("未找到ID为 %d 的任务\n", id)
		return
	}
	
	oldPriority := task.Priority
	task.Priority = priority
	task.UpdatedAt = time.Now()
	
	fmt.Printf("任务优先级已更新: %s (%s -> %s)\n", task.Title, oldPriority, priority)
	tm.saveToFile()
}

// 列出任务
func (tm *TodoManager) listTasks(filter string) {
	if len(tm.tasks) == 0 {
		fmt.Println("没有任务")
		return
	}
	
	// 过滤任务
	var filteredTasks []Task
	for _, task := range tm.tasks {
		switch filter {
		case "pending":
			if task.Status == Pending {
				filteredTasks = append(filteredTasks, task)
			}
		case "progress":
			if task.Status == InProgress {
				filteredTasks = append(filteredTasks, task)
			}
		case "completed":
			if task.Status == Completed {
				filteredTasks = append(filteredTasks, task)
			}
		case "high":
			if task.Priority == High || task.Priority == Urgent {
				filteredTasks = append(filteredTasks, task)
			}
		case "overdue":
			if task.DueDate != nil && task.DueDate.Before(time.Now()) && task.Status != Completed {
				filteredTasks = append(filteredTasks, task)
			}
		default:
			filteredTasks = append(filteredTasks, task)
		}
	}
	
	if len(filteredTasks) == 0 {
		fmt.Printf("没有符合条件的任务 (过滤器: %s)\n", filter)
		return
	}
	
	// 按优先级和创建时间排序
	sort.Slice(filteredTasks, func(i, j int) bool {
		if filteredTasks[i].Priority != filteredTasks[j].Priority {
			return filteredTasks[i].Priority > filteredTasks[j].Priority
		}
		return filteredTasks[i].CreatedAt.Before(filteredTasks[j].CreatedAt)
	})
	
	fmt.Printf("\n=== 任务列表 (过滤器: %s) ===\n", filter)
	fmt.Printf("%-4s %-20s %-8s %-8s %-12s %-12s %s\n", 
		"ID", "标题", "优先级", "状态", "创建时间", "截止时间", "标签")
	fmt.Println(strings.Repeat("-", 80))
	
	for _, task := range filteredTasks {
		dueStr := "无"
		if task.DueDate != nil {
			dueStr = task.DueDate.Format("01-02")
			if task.DueDate.Before(time.Now()) && task.Status != Completed {
				dueStr += " (逾期)"
			}
		}
		
		tagsStr := strings.Join(task.Tags, ",")
		if len(tagsStr) > 15 {
			tagsStr = tagsStr[:12] + "..."
		}
		
		title := task.Title
		if len(title) > 18 {
			title = title[:15] + "..."
		}
		
		fmt.Printf("%-4d %-20s %-8s %-8s %-12s %-12s %s\n",
			task.ID, title, task.Priority, task.Status,
			task.CreatedAt.Format("01-02 15:04"),
			dueStr, tagsStr)
	}
	fmt.Printf("\n总计: %d 个任务\n", len(filteredTasks))
}

// 显示任务详情
func (tm *TodoManager) showTaskDetail(id int) {
	task, _ := tm.findTaskByID(id)
	if task == nil {
		fmt.Printf("未找到ID为 %d 的任务\n", id)
		return
	}
	
	fmt.Printf("\n=== 任务详情 ===\n")
	fmt.Printf("ID: %d\n", task.ID)
	fmt.Printf("标题: %s\n", task.Title)
	fmt.Printf("描述: %s\n", task.Description)
	fmt.Printf("优先级: %s\n", task.Priority)
	fmt.Printf("状态: %s\n", task.Status)
	fmt.Printf("创建时间: %s\n", task.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("更新时间: %s\n", task.UpdatedAt.Format("2006-01-02 15:04:05"))
	
	if task.DueDate != nil {
		fmt.Printf("截止时间: %s", task.DueDate.Format("2006-01-02"))
		if task.DueDate.Before(time.Now()) && task.Status != Completed {
			fmt.Printf(" (已逾期)")
		}
		fmt.Println()
	}
	
	if len(task.Tags) > 0 {
		fmt.Printf("标签: %s\n", strings.Join(task.Tags, ", "))
	}
}

// 搜索任务
func (tm *TodoManager) searchTasks(keyword string) {
	var matchedTasks []Task
	keyword = strings.ToLower(keyword)
	
	for _, task := range tm.tasks {
		if strings.Contains(strings.ToLower(task.Title), keyword) ||
		   strings.Contains(strings.ToLower(task.Description), keyword) {
			matchedTasks = append(matchedTasks, task)
		}
		
		// 搜索标签
		for _, tag := range task.Tags {
			if strings.Contains(strings.ToLower(tag), keyword) {
				matchedTasks = append(matchedTasks, task)
				break
			}
		}
	}
	
	if len(matchedTasks) == 0 {
		fmt.Printf("没有找到包含 '%s' 的任务\n", keyword)
		return
	}
	
	fmt.Printf("\n=== 搜索结果: '%s' ===\n", keyword)
	for _, task := range matchedTasks {
		fmt.Printf("ID: %d, 标题: %s, 状态: %s\n", task.ID, task.Title, task.Status)
	}
}

// 显示统计信息
func (tm *TodoManager) showStatistics() {
	if len(tm.tasks) == 0 {
		fmt.Println("没有任务统计信息")
		return
	}
	
	var pending, inProgress, completed, cancelled int
	var low, medium, high, urgent int
	var overdue int
	
	now := time.Now()
	
	for _, task := range tm.tasks {
		// 状态统计
		switch task.Status {
		case Pending:
			pending++
		case InProgress:
			inProgress++
		case Completed:
			completed++
		case Cancelled:
			cancelled++
		}
		
		// 优先级统计
		switch task.Priority {
		case Low:
			low++
		case Medium:
			medium++
		case High:
			high++
		case Urgent:
			urgent++
		}
		
		// 逾期统计
		if task.DueDate != nil && task.DueDate.Before(now) && task.Status != Completed {
			overdue++
		}
	}
	
	fmt.Printf("\n=== 任务统计 ===\n")
	fmt.Printf("总任务数: %d\n", len(tm.tasks))
	fmt.Printf("\n状态分布:\n")
	fmt.Printf("  待办: %d\n", pending)
	fmt.Printf("  进行中: %d\n", inProgress)
	fmt.Printf("  已完成: %d\n", completed)
	fmt.Printf("  已取消: %d\n", cancelled)
	
	fmt.Printf("\n优先级分布:\n")
	fmt.Printf("  低: %d\n", low)
	fmt.Printf("  中: %d\n", medium)
	fmt.Printf("  高: %d\n", high)
	fmt.Printf("  紧急: %d\n", urgent)
	
	if overdue > 0 {
		fmt.Printf("\n⚠️  逾期任务: %d\n", overdue)
	}
	
	if completed > 0 {
		completionRate := float64(completed) / float64(len(tm.tasks)) * 100
		fmt.Printf("\n完成率: %.1f%%\n", completionRate)
	}
}

// 解析优先级
func parsePriority(s string) Priority {
	switch strings.ToLower(s) {
	case "low", "低", "1":
		return Low
	case "medium", "中", "2":
		return Medium
	case "high", "高", "3":
		return High
	case "urgent", "紧急", "4":
		return Urgent
	default:
		return Medium
	}
}

// 解析状态
func parseStatus(s string) Status {
	switch strings.ToLower(s) {
	case "pending", "待办", "0":
		return Pending
	case "progress", "进行中", "1":
		return InProgress
	case "completed", "完成", "2":
		return Completed
	case "cancelled", "取消", "3":
		return Cancelled
	default:
		return Pending
	}
}

// 显示帮助信息
func showHelp() {
	fmt.Println(`
=== 待办事项管理器帮助 ===

任务管理:
  add <标题> [描述]           添加新任务
  list [filter]              列出任务 (filter: all, pending, progress, completed, high, overdue)
  show <ID>                  显示任务详情
  delete <ID>                删除任务
  search <关键词>            搜索任务

任务操作:
  start <ID>                 开始任务 (设为进行中)
  complete <ID>              完成任务
  cancel <ID>                取消任务
  priority <ID> <优先级>     设置优先级 (low/medium/high/urgent)
  due <ID> <日期>            设置截止日期 (格式: 2023-12-31)

信息查看:
  stats                      显示统计信息
  help                       显示帮助
  exit                       退出程序

示例:
  > add 学习Go语言 完成Go语言基础教程
  > list pending
  > priority 1 high
  > due 1 2023-12-31
  > start 1
  > complete 1
`)
}

// 主程序循环
func (tm *TodoManager) run() {
	fmt.Println("=== Go语言待办事项管理器 ===")
	fmt.Println("输入 'help' 查看帮助，输入 'exit' 退出")
	fmt.Println()
	
	scanner := bufio.NewScanner(os.Stdin)
	
	for {
		fmt.Print("todo> ")
		
		if !scanner.Scan() {
			break
		}
		
		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}
		
		parts := strings.Fields(input)
		command := strings.ToLower(parts[0])
		
		switch command {
		case "help", "h":
			showHelp()
			
		case "add":
			if len(parts) < 2 {
				fmt.Println("用法: add <标题> [描述]")
				continue
			}
			title := parts[1]
			description := ""
			if len(parts) > 2 {
				description = strings.Join(parts[2:], " ")
			}
			tm.addTask(title, description, Medium, nil)
			
		case "list", "ls":
			filter := "all"
			if len(parts) > 1 {
				filter = parts[1]
			}
			tm.listTasks(filter)
			
		case "show", "detail":
			if len(parts) < 2 {
				fmt.Println("用法: show <ID>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				tm.showTaskDetail(id)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "delete", "del", "rm":
			if len(parts) < 2 {
				fmt.Println("用法: delete <ID>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				tm.deleteTask(id)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "start":
			if len(parts) < 2 {
				fmt.Println("用法: start <ID>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				tm.updateTaskStatus(id, InProgress)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "complete", "done":
			if len(parts) < 2 {
				fmt.Println("用法: complete <ID>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				tm.updateTaskStatus(id, Completed)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "cancel":
			if len(parts) < 2 {
				fmt.Println("用法: cancel <ID>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				tm.updateTaskStatus(id, Cancelled)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "priority":
			if len(parts) < 3 {
				fmt.Println("用法: priority <ID> <优先级>")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				priority := parsePriority(parts[2])
				tm.updateTaskPriority(id, priority)
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "due":
			if len(parts) < 3 {
				fmt.Println("用法: due <ID> <日期> (格式: 2023-12-31)")
				continue
			}
			if id, err := strconv.Atoi(parts[1]); err == nil {
				if dueDate, err := time.Parse("2006-01-02", parts[2]); err == nil {
					tm.setTaskDueDate(id, dueDate)
				} else {
					fmt.Println("无效的日期格式，请使用 YYYY-MM-DD")
				}
			} else {
				fmt.Println("无效的任务ID")
			}
			
		case "search":
			if len(parts) < 2 {
				fmt.Println("用法: search <关键词>")
				continue
			}
			keyword := strings.Join(parts[1:], " ")
			tm.searchTasks(keyword)
			
		case "stats", "statistics":
			tm.showStatistics()
			
		case "exit", "quit", "q":
			fmt.Println("感谢使用待办事项管理器！")
			return
			
		default:
			fmt.Printf("未知命令: %s\n", command)
			fmt.Println("输入 'help' 查看可用命令")
		}
	}
	
	if err := scanner.Err(); err != nil {
		fmt.Printf("读取输入时出错: %v\n", err)
	}
}

func main() {
	todoManager := NewTodoManager("todos.json")
	todoManager.run()
}

/*
运行命令: conda activate golang && go run 10_projects/02_todo_app.go

待办事项管理器特性：

1. 任务管理：
   - 添加、删除、查看任务
   - 任务标题和描述
   - 任务ID自动生成

2. 状态管理：
   - 待办、进行中、已完成、已取消
   - 状态转换和更新
   - 状态过滤显示

3. 优先级系统：
   - 低、中、高、紧急四个级别
   - 优先级排序显示
   - 优先级过滤

4. 时间管理：
   - 创建时间和更新时间
   - 截止日期设置
   - 逾期任务提醒

5. 数据持久化：
   - JSON文件存储
   - 自动保存和加载
   - 数据完整性保护

6. 搜索和过滤：
   - 关键词搜索
   - 多种过滤条件
   - 灵活的查询方式

7. 统计功能：
   - 任务状态统计
   - 优先级分布
   - 完成率计算

8. 用户界面：
   - 命令行交互
   - 表格化显示
   - 友好的错误提示

使用示例：
> add 学习Go语言 完成Go语言基础教程
任务已添加 (ID: 1): 学习Go语言
> priority 1 high
任务优先级已更新: 学习Go语言 (中 -> 高)
> due 1 2023-12-31
任务截止日期已设置: 学习Go语言 -> 2023-12-31
> start 1
任务状态已更新: 学习Go语言 (待办 -> 进行中)
> list
=== 任务列表 (过滤器: all) ===
ID   标题                 优先级   状态     创建时间      截止时间      标签
--------------------------------------------------------------------------------
1    学习Go语言           高       进行中   12-01 10:30   12-31        

总计: 1 个任务

这个项目展示了：
- 结构体和枚举的使用
- JSON序列化和文件操作
- 命令行参数解析
- 数据排序和过滤
- 时间处理和格式化
- 用户交互设计
- 错误处理和验证
- 数据持久化方案
*/
