// 03_select_context.go - Go语言select语句和context包
// 这个文件演示了select语句的高级用法和context包的使用

package main

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// 1. 基本select使用
func basicSelectDemo() {
	fmt.Println("--- 基本select使用 ---")
	
	ch1 := make(chan string, 1)
	ch2 := make(chan string, 1)
	
	// 发送数据到通道
	ch1 <- "来自通道1"
	ch2 <- "来自通道2"
	
	// select随机选择一个就绪的case
	select {
	case msg1 := <-ch1:
		fmt.Printf("收到: %s\n", msg1)
	case msg2 := <-ch2:
		fmt.Printf("收到: %s\n", msg2)
	}
	
	// 接收剩余的消息
	select {
	case msg1 := <-ch1:
		fmt.Printf("收到: %s\n", msg1)
	case msg2 := <-ch2:
		fmt.Printf("收到: %s\n", msg2)
	default:
		fmt.Println("没有更多消息")
	}
}

// 2. 超时处理
func timeoutDemo() {
	fmt.Println("\n--- 超时处理 ---")
	
	// 模拟慢速操作
	slowOperation := func(delay time.Duration) <-chan string {
		result := make(chan string, 1)
		go func() {
			time.Sleep(delay)
			result <- "操作完成"
		}()
		return result
	}
	
	// 快速操作（1秒内完成）
	fmt.Println("快速操作:")
	select {
	case result := <-slowOperation(500 * time.Millisecond):
		fmt.Printf("结果: %s\n", result) // 输出: 结果: 操作完成
	case <-time.After(1 * time.Second):
		fmt.Println("操作超时")
	}
	
	// 慢速操作（超过1秒）
	fmt.Println("慢速操作:")
	select {
	case result := <-slowOperation(2 * time.Second):
		fmt.Printf("结果: %s\n", result)
	case <-time.After(1 * time.Second):
		fmt.Println("操作超时") // 输出: 操作超时
	}
}

// 3. 非阻塞操作
func nonBlockingDemo() {
	fmt.Println("\n--- 非阻塞操作 ---")
	
	ch := make(chan int, 1)
	
	// 非阻塞发送
	select {
	case ch <- 42:
		fmt.Println("成功发送数据")
	default:
		fmt.Println("通道已满，无法发送")
	}
	
	// 非阻塞接收
	select {
	case value := <-ch:
		fmt.Printf("成功接收数据: %d\n", value) // 输出: 成功接收数据: 42
	default:
		fmt.Println("通道为空，无法接收")
	}
	
	// 再次尝试非阻塞接收
	select {
	case value := <-ch:
		fmt.Printf("成功接收数据: %d\n", value)
	default:
		fmt.Println("通道为空，无法接收") // 输出: 通道为空，无法接收
	}
}

// 4. 多路复用
func multiplexingDemo() {
	fmt.Println("\n--- 多路复用 ---")
	
	// 创建多个数据源
	source1 := make(chan string)
	source2 := make(chan string)
	source3 := make(chan string)
	quit := make(chan bool)
	
	// 启动数据源
	go func() {
		for i := 1; i <= 3; i++ {
			source1 <- fmt.Sprintf("源1-数据%d", i)
			time.Sleep(300 * time.Millisecond)
		}
		close(source1)
	}()
	
	go func() {
		for i := 1; i <= 2; i++ {
			source2 <- fmt.Sprintf("源2-数据%d", i)
			time.Sleep(500 * time.Millisecond)
		}
		close(source2)
	}()
	
	go func() {
		for i := 1; i <= 4; i++ {
			source3 <- fmt.Sprintf("源3-数据%d", i)
			time.Sleep(200 * time.Millisecond)
		}
		close(source3)
	}()
	
	// 设置超时
	go func() {
		time.Sleep(3 * time.Second)
		quit <- true
	}()
	
	// 多路复用接收数据
	activeChannels := 3
	for activeChannels > 0 {
		select {
		case data, ok := <-source1:
			if ok {
				fmt.Printf("收到: %s\n", data)
			} else {
				fmt.Println("源1已关闭")
				source1 = nil
				activeChannels--
			}
		case data, ok := <-source2:
			if ok {
				fmt.Printf("收到: %s\n", data)
			} else {
				fmt.Println("源2已关闭")
				source2 = nil
				activeChannels--
			}
		case data, ok := <-source3:
			if ok {
				fmt.Printf("收到: %s\n", data)
			} else {
				fmt.Println("源3已关闭")
				source3 = nil
				activeChannels--
			}
		case <-quit:
			fmt.Println("接收超时，退出")
			return
		}
	}
	fmt.Println("所有数据源已关闭")
}

// 5. Context基础使用
func contextBasicDemo() {
	fmt.Println("\n--- Context基础使用 ---")
	
	// 创建带取消功能的context
	ctx, cancel := context.WithCancel(context.Background())
	
	// 启动一个可取消的协程
	go func() {
		for i := 1; i <= 10; i++ {
			select {
			case <-ctx.Done():
				fmt.Printf("协程被取消，原因: %v\n", ctx.Err())
				return
			default:
				fmt.Printf("协程工作中... %d\n", i)
				time.Sleep(200 * time.Millisecond)
			}
		}
		fmt.Println("协程正常完成")
	}()
	
	// 2秒后取消协程
	time.Sleep(1 * time.Second)
	fmt.Println("取消协程")
	cancel()
	
	time.Sleep(500 * time.Millisecond) // 等待协程退出
}

// 6. Context超时
func contextTimeoutDemo() {
	fmt.Println("\n--- Context超时 ---")
	
	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel() // 确保资源被释放
	
	// 模拟长时间运行的任务
	longTask := func(ctx context.Context) error {
		for i := 1; i <= 5; i++ {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				fmt.Printf("任务进行中... %d\n", i)
				time.Sleep(300 * time.Millisecond)
			}
		}
		return nil
	}
	
	if err := longTask(ctx); err != nil {
		fmt.Printf("任务被中断: %v\n", err) // 输出: 任务被中断: context deadline exceeded
	} else {
		fmt.Println("任务完成")
	}
}

// 7. Context传递值
func contextValueDemo() {
	fmt.Println("\n--- Context传递值 ---")
	
	type key string
	const userKey key = "user"
	const requestIDKey key = "requestID"
	
	// 创建带值的context
	ctx := context.Background()
	ctx = context.WithValue(ctx, userKey, "张三")
	ctx = context.WithValue(ctx, requestIDKey, "req-12345")
	
	// 处理请求的函数
	processRequest := func(ctx context.Context) {
		user := ctx.Value(userKey)
		requestID := ctx.Value(requestIDKey)
		
		fmt.Printf("处理请求 - 用户: %v, 请求ID: %v\n", user, requestID)
		
		// 调用其他函数，传递context
		handleDatabase(ctx)
		handleCache(ctx)
	}
	
	handleDatabase := func(ctx context.Context) {
		user := ctx.Value(userKey)
		requestID := ctx.Value(requestIDKey)
		fmt.Printf("数据库操作 - 用户: %v, 请求ID: %v\n", user, requestID)
	}
	
	handleCache := func(ctx context.Context) {
		user := ctx.Value(userKey)
		requestID := ctx.Value(requestIDKey)
		fmt.Printf("缓存操作 - 用户: %v, 请求ID: %v\n", user, requestID)
	}
	
	processRequest(ctx)
}

// 8. Context链式取消
func contextChainDemo() {
	fmt.Println("\n--- Context链式取消 ---")
	
	// 创建根context
	rootCtx, rootCancel := context.WithCancel(context.Background())
	
	// 创建子context
	childCtx, childCancel := context.WithCancel(rootCtx)
	
	// 创建孙子context
	grandChildCtx, grandChildCancel := context.WithTimeout(childCtx, 2*time.Second)
	
	var wg sync.WaitGroup
	
	// 启动协程监听各级context
	wg.Add(3)
	
	go func() {
		defer wg.Done()
		<-rootCtx.Done()
		fmt.Printf("根context被取消: %v\n", rootCtx.Err())
	}()
	
	go func() {
		defer wg.Done()
		<-childCtx.Done()
		fmt.Printf("子context被取消: %v\n", childCtx.Err())
	}()
	
	go func() {
		defer wg.Done()
		<-grandChildCtx.Done()
		fmt.Printf("孙子context被取消: %v\n", grandChildCtx.Err())
	}()
	
	// 1秒后取消根context
	time.Sleep(1 * time.Second)
	fmt.Println("取消根context")
	rootCancel()
	
	wg.Wait()
	
	// 清理资源
	childCancel()
	grandChildCancel()
}

// 9. 实际应用：HTTP请求处理
func httpRequestDemo() {
	fmt.Println("\n--- 实际应用：HTTP请求处理 ---")
	
	// 模拟HTTP请求处理
	handleRequest := func(ctx context.Context, requestID string) {
		// 添加请求ID到context
		ctx = context.WithValue(ctx, "requestID", requestID)
		
		fmt.Printf("开始处理请求: %s\n", requestID)
		
		// 并行处理多个任务
		var wg sync.WaitGroup
		wg.Add(3)
		
		// 任务1：验证用户
		go func() {
			defer wg.Done()
			if err := validateUser(ctx); err != nil {
				fmt.Printf("用户验证失败: %v\n", err)
			}
		}()
		
		// 任务2：获取数据
		go func() {
			defer wg.Done()
			if err := fetchData(ctx); err != nil {
				fmt.Printf("数据获取失败: %v\n", err)
			}
		}()
		
		// 任务3：记录日志
		go func() {
			defer wg.Done()
			if err := logRequest(ctx); err != nil {
				fmt.Printf("日志记录失败: %v\n", err)
			}
		}()
		
		wg.Wait()
		fmt.Printf("请求处理完成: %s\n", requestID)
	}
	
	validateUser := func(ctx context.Context) error {
		requestID := ctx.Value("requestID")
		select {
		case <-time.After(200 * time.Millisecond):
			fmt.Printf("用户验证完成 [%v]\n", requestID)
			return nil
		case <-ctx.Done():
			return ctx.Err()
		}
	}
	
	fetchData := func(ctx context.Context) error {
		requestID := ctx.Value("requestID")
		select {
		case <-time.After(300 * time.Millisecond):
			fmt.Printf("数据获取完成 [%v]\n", requestID)
			return nil
		case <-ctx.Done():
			return ctx.Err()
		}
	}
	
	logRequest := func(ctx context.Context) error {
		requestID := ctx.Value("requestID")
		select {
		case <-time.After(100 * time.Millisecond):
			fmt.Printf("日志记录完成 [%v]\n", requestID)
			return nil
		case <-ctx.Done():
			return ctx.Err()
		}
	}
	
	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()
	
	handleRequest(ctx, "REQ-001")
}

// 10. 工作池与context
func workerPoolWithContextDemo() {
	fmt.Println("\n--- 工作池与context ---")
	
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	
	const numWorkers = 3
	jobs := make(chan int, 10)
	results := make(chan int, 10)
	
	var wg sync.WaitGroup
	
	// 启动工作者
	for i := 1; i <= numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for {
				select {
				case job, ok := <-jobs:
					if !ok {
						fmt.Printf("工作者 %d 退出（任务通道关闭）\n", workerID)
						return
					}
					fmt.Printf("工作者 %d 处理任务 %d\n", workerID, job)
					time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
					
					select {
					case results <- job * job:
					case <-ctx.Done():
						fmt.Printf("工作者 %d 退出（context取消）\n", workerID)
						return
					}
				case <-ctx.Done():
					fmt.Printf("工作者 %d 退出（context取消）\n", workerID)
					return
				}
			}
		}(i)
	}
	
	// 发送任务
	go func() {
		defer close(jobs)
		for i := 1; i <= 10; i++ {
			select {
			case jobs <- i:
				fmt.Printf("发送任务 %d\n", i)
			case <-ctx.Done():
				fmt.Println("停止发送任务（context取消）")
				return
			}
			time.Sleep(100 * time.Millisecond)
		}
	}()
	
	// 收集结果
	go func() {
		defer close(results)
		wg.Wait()
	}()
	
	// 接收结果
	for result := range results {
		fmt.Printf("收到结果: %d\n", result)
	}
}

func main() {
	fmt.Println("=== Go语言select语句和context包示例 ===")
	
	rand.Seed(time.Now().UnixNano())
	
	// 1. 基本select使用
	basicSelectDemo()
	
	// 2. 超时处理
	timeoutDemo()
	
	// 3. 非阻塞操作
	nonBlockingDemo()
	
	// 4. 多路复用
	multiplexingDemo()
	
	// 5. Context基础使用
	contextBasicDemo()
	
	// 6. Context超时
	contextTimeoutDemo()
	
	// 7. Context传递值
	contextValueDemo()
	
	// 8. Context链式取消
	contextChainDemo()
	
	// 9. 实际应用：HTTP请求处理
	httpRequestDemo()
	
	// 10. 工作池与context
	workerPoolWithContextDemo()
	
	// 11. 最佳实践
	fmt.Println("\n--- 最佳实践 ---")
	
	fmt.Println("select语句最佳实践:")
	fmt.Println("1. 使用default实现非阻塞操作")
	fmt.Println("2. 使用time.After实现超时")
	fmt.Println("3. 处理通道关闭情况")
	fmt.Println("4. 避免在select中执行耗时操作")
	fmt.Println("5. 合理使用随机选择特性")
	
	fmt.Println("\ncontext包最佳实践:")
	fmt.Println("1. 将context作为函数第一个参数")
	fmt.Println("2. 不要将context存储在结构体中")
	fmt.Println("3. 使用context.TODO()当不确定使用哪个context时")
	fmt.Println("4. context.Value应该用于请求范围的数据")
	fmt.Println("5. 总是调用cancel函数释放资源")
	fmt.Println("6. context是线程安全的")
	fmt.Println("7. 不要传递nil context")
}

/*
运行命令: conda activate golang && go run 06_concurrency/03_select_context.go

select语句和context包总结：

1. select语句特点：
   - 多路复用通道操作
   - 随机选择就绪的case
   - 支持default分支（非阻塞）
   - 可以处理发送和接收操作

2. select常见用法：
   - 超时处理：time.After
   - 非阻塞操作：default分支
   - 多通道监听
   - 协程退出控制

3. context包功能：
   - 取消信号传播
   - 超时控制
   - 截止时间设置
   - 请求范围值传递

4. context类型：
   - context.Background()：根context
   - context.WithCancel()：可取消
   - context.WithTimeout()：超时取消
   - context.WithDeadline()：截止时间取消
   - context.WithValue()：携带值

5. context最佳实践：
   - 作为函数第一个参数
   - 不存储在结构体中
   - 总是调用cancel释放资源
   - 用于请求范围的数据传递
   - 线程安全，可以并发使用

6. 应用场景：
   - HTTP请求处理
   - 数据库操作超时
   - 协程取消控制
   - 分布式系统调用链
   - 资源清理

7. 注意事项：
   - context取消会传播到子context
   - 不要传递nil context
   - context.Value不应该用于可选参数
   - 合理设置超时时间
*/
