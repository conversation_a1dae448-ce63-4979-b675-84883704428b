// 02_channels.go - Go语言通道(Channels)
// 这个文件演示了Go语言中通道的创建、使用和各种模式

package main

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// 1. 基本通道操作
func basicChannelDemo() {
	fmt.Println("--- 基本通道操作 ---")
	
	// 创建无缓冲通道
	ch := make(chan int)
	
	// 在协程中发送数据
	go func() {
		fmt.Println("发送数据: 42")
		ch <- 42 // 发送数据到通道
		fmt.Println("数据已发送")
	}()
	
	// 从通道接收数据
	fmt.Println("等待接收数据...")
	value := <-ch // 从通道接收数据
	fmt.Printf("接收到数据: %d\n", value) // 输出: 接收到数据: 42
	
	close(ch) // 关闭通道
}

// 2. 带缓冲通道
func bufferedChannelDemo() {
	fmt.Println("\n--- 带缓冲通道 ---")
	
	// 创建带缓冲的通道
	ch := make(chan string, 3)
	
	// 发送数据（不会阻塞，因为有缓冲）
	ch <- "第一条消息"
	ch <- "第二条消息"
	ch <- "第三条消息"
	
	fmt.Printf("通道长度: %d, 容量: %d\n", len(ch), cap(ch)) // 输出: 通道长度: 3, 容量: 3
	
	// 接收数据
	for i := 0; i < 3; i++ {
		msg := <-ch
		fmt.Printf("接收消息: %s\n", msg)
	}
	
	fmt.Printf("通道长度: %d, 容量: %d\n", len(ch), cap(ch)) // 输出: 通道长度: 0, 容量: 3
}

// 3. 通道方向（单向通道）
func channelDirectionDemo() {
	fmt.Println("\n--- 通道方向 ---")
	
	// 只发送通道
	sendOnly := func(ch chan<- int) {
		for i := 1; i <= 3; i++ {
			ch <- i
			fmt.Printf("发送: %d\n", i)
		}
		close(ch)
	}
	
	// 只接收通道
	receiveOnly := func(ch <-chan int) {
		for value := range ch {
			fmt.Printf("接收: %d\n", value)
		}
	}
	
	ch := make(chan int)
	
	go sendOnly(ch)   // 传递为只发送通道
	receiveOnly(ch)   // 传递为只接收通道
}

// 4. 通道关闭和range
func channelCloseDemo() {
	fmt.Println("\n--- 通道关闭和range ---")
	
	ch := make(chan int, 5)
	
	// 发送数据并关闭通道
	go func() {
		for i := 1; i <= 5; i++ {
			ch <- i * i
			fmt.Printf("发送: %d\n", i*i)
		}
		close(ch) // 关闭通道
		fmt.Println("通道已关闭")
	}()
	
	// 使用range接收数据，直到通道关闭
	fmt.Println("使用range接收数据:")
	for value := range ch {
		fmt.Printf("接收: %d\n", value)
	}
	
	// 检查通道是否关闭
	value, ok := <-ch
	fmt.Printf("通道关闭后接收: value=%d, ok=%t\n", value, ok) // 输出: value=0, ok=false
}

// 5. select语句
func selectDemo() {
	fmt.Println("\n--- select语句 ---")
	
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	// 协程1：延迟发送
	go func() {
		time.Sleep(1 * time.Second)
		ch1 <- "来自通道1的消息"
	}()
	
	// 协程2：延迟发送
	go func() {
		time.Sleep(2 * time.Second)
		ch2 <- "来自通道2的消息"
	}()
	
	// 使用select等待多个通道
	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Printf("收到: %s\n", msg1)
		case msg2 := <-ch2:
			fmt.Printf("收到: %s\n", msg2)
		case <-time.After(3 * time.Second):
			fmt.Println("超时")
		}
	}
}

// 6. 非阻塞通道操作
func nonBlockingDemo() {
	fmt.Println("\n--- 非阻塞通道操作 ---")
	
	ch := make(chan int, 1)
	
	// 非阻塞发送
	select {
	case ch <- 42:
		fmt.Println("成功发送数据")
	default:
		fmt.Println("通道已满，无法发送")
	}
	
	// 非阻塞接收
	select {
	case value := <-ch:
		fmt.Printf("成功接收数据: %d\n", value)
	default:
		fmt.Println("通道为空，无法接收")
	}
	
	// 再次尝试非阻塞接收
	select {
	case value := <-ch:
		fmt.Printf("成功接收数据: %d\n", value)
	default:
		fmt.Println("通道为空，无法接收") // 输出: 通道为空，无法接收
	}
}

// 7. 工作池模式
func workerPoolDemo() {
	fmt.Println("\n--- 工作池模式 ---")
	
	const numWorkers = 3
	const numJobs = 10
	
	jobs := make(chan int, numJobs)
	results := make(chan int, numJobs)
	
	// 启动工作者
	var wg sync.WaitGroup
	for w := 1; w <= numWorkers; w++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for job := range jobs {
				fmt.Printf("工作者 %d 处理任务 %d\n", id, job)
				time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
				results <- job * 2
			}
		}(w)
	}
	
	// 发送任务
	for j := 1; j <= numJobs; j++ {
		jobs <- j
	}
	close(jobs)
	
	// 等待所有工作者完成
	go func() {
		wg.Wait()
		close(results)
	}()
	
	// 收集结果
	fmt.Println("收集结果:")
	for result := range results {
		fmt.Printf("结果: %d\n", result)
	}
}

// 8. 扇入模式（Fan-in）
func fanInDemo() {
	fmt.Println("\n--- 扇入模式 ---")
	
	// 扇入函数：合并多个通道到一个通道
	fanIn := func(input1, input2 <-chan string) <-chan string {
		output := make(chan string)
		go func() {
			defer close(output)
			for {
				select {
				case msg, ok := <-input1:
					if !ok {
						input1 = nil
					} else {
						output <- msg
					}
				case msg, ok := <-input2:
					if !ok {
						input2 = nil
					} else {
						output <- msg
					}
				}
				if input1 == nil && input2 == nil {
					break
				}
			}
		}()
		return output
	}
	
	// 创建两个输入通道
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	// 启动生产者
	go func() {
		defer close(ch1)
		for i := 1; i <= 3; i++ {
			ch1 <- fmt.Sprintf("通道1-消息%d", i)
			time.Sleep(100 * time.Millisecond)
		}
	}()
	
	go func() {
		defer close(ch2)
		for i := 1; i <= 3; i++ {
			ch2 <- fmt.Sprintf("通道2-消息%d", i)
			time.Sleep(150 * time.Millisecond)
		}
	}()
	
	// 合并通道并接收消息
	merged := fanIn(ch1, ch2)
	for msg := range merged {
		fmt.Printf("合并后收到: %s\n", msg)
	}
}

// 9. 扇出模式（Fan-out）
func fanOutDemo() {
	fmt.Println("\n--- 扇出模式 ---")
	
	// 扇出函数：将一个通道分发到多个通道
	fanOut := func(input <-chan int, output1, output2 chan<- int) {
		defer close(output1)
		defer close(output2)
		
		for value := range input {
			// 随机分发到两个输出通道之一
			if rand.Intn(2) == 0 {
				output1 <- value
			} else {
				output2 <- value
			}
		}
	}
	
	// 创建通道
	input := make(chan int)
	output1 := make(chan int)
	output2 := make(chan int)
	
	// 启动扇出
	go fanOut(input, output1, output2)
	
	// 启动接收者
	var wg sync.WaitGroup
	wg.Add(2)
	
	go func() {
		defer wg.Done()
		for value := range output1 {
			fmt.Printf("输出1收到: %d\n", value)
		}
	}()
	
	go func() {
		defer wg.Done()
		for value := range output2 {
			fmt.Printf("输出2收到: %d\n", value)
		}
	}()
	
	// 发送数据
	go func() {
		defer close(input)
		for i := 1; i <= 6; i++ {
			input <- i
			time.Sleep(100 * time.Millisecond)
		}
	}()
	
	wg.Wait()
}

// 10. 管道模式（Pipeline）
func pipelineDemo() {
	fmt.Println("\n--- 管道模式 ---")
	
	// 阶段1：生成数字
	generate := func(nums ...int) <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for _, n := range nums {
				out <- n
			}
		}()
		return out
	}
	
	// 阶段2：平方
	square := func(in <-chan int) <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for n := range in {
				out <- n * n
			}
		}()
		return out
	}
	
	// 阶段3：过滤偶数
	filterEven := func(in <-chan int) <-chan int {
		out := make(chan int)
		go func() {
			defer close(out)
			for n := range in {
				if n%2 == 0 {
					out <- n
				}
			}
		}()
		return out
	}
	
	// 构建管道
	numbers := generate(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
	squared := square(numbers)
	filtered := filterEven(squared)
	
	// 消费结果
	fmt.Println("管道结果（偶数的平方）:")
	for result := range filtered {
		fmt.Printf("结果: %d\n", result)
	}
}

// 11. 超时和取消
func timeoutDemo() {
	fmt.Println("\n--- 超时和取消 ---")
	
	// 模拟长时间运行的任务
	longRunningTask := func() <-chan string {
		result := make(chan string)
		go func() {
			defer close(result)
			time.Sleep(2 * time.Second) // 模拟长时间任务
			result <- "任务完成"
		}()
		return result
	}
	
	// 使用超时
	select {
	case result := <-longRunningTask():
		fmt.Printf("收到结果: %s\n", result)
	case <-time.After(1 * time.Second):
		fmt.Println("任务超时") // 输出: 任务超时
	}
}

func main() {
	fmt.Println("=== Go语言通道(Channels)示例 ===")
	
	// 设置随机种子
	rand.Seed(time.Now().UnixNano())
	
	// 1. 基本通道操作
	basicChannelDemo()
	
	// 2. 带缓冲通道
	bufferedChannelDemo()
	
	// 3. 通道方向
	channelDirectionDemo()
	
	// 4. 通道关闭和range
	channelCloseDemo()
	
	// 5. select语句
	selectDemo()
	
	// 6. 非阻塞通道操作
	nonBlockingDemo()
	
	// 7. 工作池模式
	workerPoolDemo()
	
	// 8. 扇入模式
	fanInDemo()
	
	// 9. 扇出模式
	fanOutDemo()
	
	// 10. 管道模式
	pipelineDemo()
	
	// 11. 超时和取消
	timeoutDemo()
	
	// 12. 通道最佳实践
	fmt.Println("\n--- 通道最佳实践 ---")
	
	fmt.Println("Go通道最佳实践:")
	fmt.Println("1. 发送者负责关闭通道")
	fmt.Println("2. 不要从接收端关闭通道")
	fmt.Println("3. 不要向已关闭的通道发送数据")
	fmt.Println("4. 使用range循环接收数据直到通道关闭")
	fmt.Println("5. 使用select处理多个通道操作")
	fmt.Println("6. 使用带缓冲通道避免协程阻塞")
	fmt.Println("7. 使用单向通道限制操作类型")
	fmt.Println("8. 使用context包处理取消和超时")
	fmt.Println("9. 避免通道泄漏，确保通道能被垃圾回收")
	fmt.Println("10. 合理使用通道容量，避免内存浪费")
}

/*
运行命令: conda activate golang && go run 06_concurrency/02_channels.go

Go语言通道(Channels)总结：

1. 通道类型：
   - 无缓冲通道：make(chan T)
   - 带缓冲通道：make(chan T, capacity)
   - 单向通道：chan<- T (只发送), <-chan T (只接收)

2. 通道操作：
   - 发送：ch <- value
   - 接收：value := <-ch 或 value, ok := <-ch
   - 关闭：close(ch)
   - 长度：len(ch)
   - 容量：cap(ch)

3. 通道特性：
   - 类型安全
   - 先进先出(FIFO)
   - 线程安全
   - 阻塞语义

4. select语句：
   - 多路复用通道操作
   - 非阻塞操作（配合default）
   - 超时处理（配合time.After）
   - 随机选择（多个case同时就绪）

5. 常见模式：
   - 工作池：多个工作者处理任务
   - 扇入：多个输入合并到一个输出
   - 扇出：一个输入分发到多个输出
   - 管道：数据流水线处理

6. 最佳实践：
   - 发送者关闭通道
   - 使用range接收数据
   - 避免通道泄漏
   - 合理使用缓冲
   - 使用单向通道限制操作

7. 注意事项：
   - 向已关闭通道发送会panic
   - 关闭已关闭通道会panic
   - 接收已关闭通道返回零值
   - nil通道永远阻塞

8. 应用场景：
   - 协程间通信
   - 同步协程
   - 数据流处理
   - 事件通知
   - 资源池管理
*/
