// 01_goroutines.go - Go语言协程(Goroutines)
// 这个文件演示了Go语言中协程的创建、使用和管理

package main

import (
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"time"
)

// 1. 简单的协程函数
func sayHello(name string, times int) {
	for i := 0; i < times; i++ {
		fmt.Printf("Hello %s! (%d)\n", name, i+1)
		time.Sleep(100 * time.Millisecond) // 模拟工作
	}
}

// 2. 带返回值的工作函数（通过channel返回）
func calculateSum(start, end int, result chan<- int) {
	sum := 0
	for i := start; i <= end; i++ {
		sum += i
	}
	fmt.Printf("计算 %d 到 %d 的和: %d\n", start, end, sum)
	result <- sum // 将结果发送到channel
}

// 3. 模拟网络请求
func fetchData(url string, delay time.Duration, wg *sync.WaitGroup) {
	defer wg.Done() // 确保在函数结束时调用Done()
	
	fmt.Printf("开始请求: %s\n", url)
	time.Sleep(delay) // 模拟网络延迟
	fmt.Printf("完成请求: %s (耗时: %v)\n", url, delay)
}

// 4. 生产者函数
func producer(name string, products chan<- string, count int) {
	defer close(products) // 生产完毕后关闭channel
	
	for i := 1; i <= count; i++ {
		product := fmt.Sprintf("%s-产品%d", name, i)
		products <- product
		fmt.Printf("生产者 %s 生产了: %s\n", name, product)
		time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
	}
	fmt.Printf("生产者 %s 完成生产\n", name)
}

// 5. 消费者函数
func consumer(name string, products <-chan string, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for product := range products {
		fmt.Printf("消费者 %s 消费了: %s\n", name, product)
		time.Sleep(time.Duration(rand.Intn(300)) * time.Millisecond)
	}
	fmt.Printf("消费者 %s 完成消费\n", name)
}

// 6. 工作池模式
func worker(id int, jobs <-chan int, results chan<- int, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for job := range jobs {
		fmt.Printf("工作者 %d 开始处理任务 %d\n", id, job)
		
		// 模拟工作
		time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
		result := job * job // 简单的计算
		
		fmt.Printf("工作者 %d 完成任务 %d，结果: %d\n", id, job, result)
		results <- result
	}
}

// 7. 递归协程示例：并行计算斐波那契数列
func fibonacci(n int, result chan<- int) {
	defer close(result)
	
	if n <= 1 {
		result <- n
		return
	}
	
	// 创建两个channel来接收子问题的结果
	ch1 := make(chan int)
	ch2 := make(chan int)
	
	// 并行计算两个子问题
	go fibonacci(n-1, ch1)
	go fibonacci(n-2, ch2)
	
	// 等待两个结果并相加
	result <- <-ch1 + <-ch2
}

// 8. 协程泄漏示例和解决方案
func leakyGoroutine() {
	fmt.Println("--- 协程泄漏示例 ---")
	
	// 错误示例：协程可能永远阻塞
	badChannel := make(chan int)
	
	go func() {
		// 这个协程会永远等待，因为没有人向channel发送数据
		fmt.Println("等待数据...")
		data := <-badChannel
		fmt.Printf("收到数据: %d\n", data)
	}()
	
	// 正确示例：使用带缓冲的channel或超时
	goodChannel := make(chan int, 1) // 带缓冲
	done := make(chan bool)
	
	go func() {
		select {
		case data := <-goodChannel:
			fmt.Printf("收到数据: %d\n", data)
		case <-time.After(1 * time.Second):
			fmt.Println("超时，退出协程")
		}
		done <- true
	}()
	
	// 等待协程完成
	<-done
	fmt.Println("协程安全退出")
}

func main() {
	fmt.Println("=== Go语言协程(Goroutines)示例 ===")
	
	// 显示当前系统信息
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	fmt.Printf("当前协程数: %d\n", runtime.NumGoroutine())
	
	// 1. 基本协程使用
	fmt.Println("\n--- 基本协程使用 ---")
	
	// 启动协程
	go sayHello("Alice", 3)
	go sayHello("Bob", 3)
	
	// 主协程也做一些工作
	for i := 0; i < 3; i++ {
		fmt.Printf("主协程工作 %d\n", i+1)
		time.Sleep(150 * time.Millisecond)
	}
	
	time.Sleep(1 * time.Second) // 等待协程完成
	
	// 2. 使用channel接收协程结果
	fmt.Println("\n--- 使用channel接收协程结果 ---")
	
	result1 := make(chan int)
	result2 := make(chan int)
	
	go calculateSum(1, 100, result1)
	go calculateSum(101, 200, result2)
	
	// 等待并收集结果
	sum1 := <-result1
	sum2 := <-result2
	totalSum := sum1 + sum2
	
	fmt.Printf("总和: %d + %d = %d\n", sum1, sum2, totalSum) // 输出: 总和: 5050 + 15050 = 20100
	
	// 3. 使用WaitGroup等待多个协程
	fmt.Println("\n--- 使用WaitGroup等待多个协程 ---")
	
	var wg sync.WaitGroup
	urls := []string{
		"https://api1.example.com",
		"https://api2.example.com",
		"https://api3.example.com",
	}
	delays := []time.Duration{
		300 * time.Millisecond,
		500 * time.Millisecond,
		200 * time.Millisecond,
	}
	
	for i, url := range urls {
		wg.Add(1) // 增加等待计数
		go fetchData(url, delays[i], &wg)
	}
	
	fmt.Println("等待所有请求完成...")
	wg.Wait() // 等待所有协程完成
	fmt.Println("所有请求已完成")
	
	// 4. 生产者-消费者模式
	fmt.Println("\n--- 生产者-消费者模式 ---")
	
	products := make(chan string, 5) // 带缓冲的channel
	var consumerWg sync.WaitGroup
	
	// 启动生产者
	go producer("工厂A", products, 5)
	
	// 启动多个消费者
	consumerWg.Add(2)
	go consumer("商店1", products, &consumerWg)
	go consumer("商店2", products, &consumerWg)
	
	consumerWg.Wait()
	fmt.Println("生产消费完成")
	
	// 5. 工作池模式
	fmt.Println("\n--- 工作池模式 ---")
	
	const numWorkers = 3
	const numJobs = 10
	
	jobs := make(chan int, numJobs)
	results := make(chan int, numJobs)
	var workerWg sync.WaitGroup
	
	// 启动工作者
	for i := 1; i <= numWorkers; i++ {
		workerWg.Add(1)
		go worker(i, jobs, results, &workerWg)
	}
	
	// 发送任务
	for i := 1; i <= numJobs; i++ {
		jobs <- i
	}
	close(jobs) // 关闭任务channel
	
	// 在另一个协程中等待工作者完成并关闭结果channel
	go func() {
		workerWg.Wait()
		close(results)
	}()
	
	// 收集结果
	fmt.Println("收集结果:")
	totalResult := 0
	for result := range results {
		totalResult += result
		fmt.Printf("收到结果: %d\n", result)
	}
	fmt.Printf("所有结果总和: %d\n", totalResult)
	
	// 6. 并行计算斐波那契数列
	fmt.Println("\n--- 并行计算斐波那契数列 ---")
	
	n := 10
	fibResult := make(chan int)
	
	fmt.Printf("计算斐波那契数列第 %d 项...\n", n)
	start := time.Now()
	
	go fibonacci(n, fibResult)
	
	result := <-fibResult
	elapsed := time.Since(start)
	
	fmt.Printf("斐波那契数列第 %d 项: %d (耗时: %v)\n", n, result, elapsed)
	
	// 7. 协程数量监控
	fmt.Println("\n--- 协程数量监控 ---")
	
	fmt.Printf("启动前协程数: %d\n", runtime.NumGoroutine())
	
	// 启动一些协程
	done := make(chan bool)
	for i := 0; i < 5; i++ {
		go func(id int) {
			fmt.Printf("协程 %d 运行中...\n", id)
			time.Sleep(500 * time.Millisecond)
			if id == 4 { // 最后一个协程发送完成信号
				done <- true
			}
		}(i)
	}
	
	fmt.Printf("启动后协程数: %d\n", runtime.NumGoroutine())
	
	<-done // 等待完成信号
	time.Sleep(100 * time.Millisecond) // 给协程一些时间退出
	
	fmt.Printf("完成后协程数: %d\n", runtime.NumGoroutine())
	
	// 8. 协程泄漏示例
	leakyGoroutine()
	
	// 9. 匿名协程
	fmt.Println("\n--- 匿名协程 ---")
	
	finished := make(chan bool)
	
	// 匿名协程
	go func() {
		fmt.Println("匿名协程开始执行")
		for i := 1; i <= 3; i++ {
			fmt.Printf("匿名协程工作 %d\n", i)
			time.Sleep(200 * time.Millisecond)
		}
		fmt.Println("匿名协程执行完成")
		finished <- true
	}()
	
	<-finished
	
	// 10. 协程与闭包
	fmt.Println("\n--- 协程与闭包 ---")
	
	var closureWg sync.WaitGroup
	
	// 错误示例：闭包捕获循环变量
	fmt.Println("错误示例（可能输出相同的值）:")
	for i := 0; i < 3; i++ {
		closureWg.Add(1)
		go func() {
			defer closureWg.Done()
			fmt.Printf("错误示例 - 协程输出: %d\n", i) // 可能都输出3
		}()
	}
	closureWg.Wait()
	
	// 正确示例：传递参数
	fmt.Println("正确示例（传递参数）:")
	for i := 0; i < 3; i++ {
		closureWg.Add(1)
		go func(id int) {
			defer closureWg.Done()
			fmt.Printf("正确示例 - 协程输出: %d\n", id)
		}(i)
	}
	closureWg.Wait()
	
	// 11. 协程最佳实践
	fmt.Println("\n--- 协程最佳实践 ---")
	
	fmt.Println("Go协程最佳实践:")
	fmt.Println("1. 使用WaitGroup或channel等待协程完成")
	fmt.Println("2. 避免协程泄漏，确保协程能够正常退出")
	fmt.Println("3. 合理控制协程数量，避免创建过多协程")
	fmt.Println("4. 使用context包处理协程取消和超时")
	fmt.Println("5. 注意闭包中的变量捕获问题")
	fmt.Println("6. 使用channel进行协程间通信")
	fmt.Println("7. 遵循'不要通过共享内存来通信，而要通过通信来共享内存'")
	fmt.Println("8. 使用工作池模式处理大量任务")
	fmt.Println("9. 监控协程数量，避免资源泄漏")
	fmt.Println("10. 合理使用带缓冲和无缓冲channel")
	
	fmt.Printf("\n程序结束时协程数: %d\n", runtime.NumGoroutine())
}

/*
运行命令: conda activate golang && go run 06_concurrency/01_goroutines.go

Go语言协程(Goroutines)总结：

1. 协程特点：
   - 轻量级线程，由Go运行时管理
   - 栈大小动态增长（初始2KB）
   - M:N调度模型（M个协程映射到N个OS线程）
   - 协作式调度

2. 创建协程：
   - go functionName(args)
   - go func() { ... }()
   - 协程立即开始执行

3. 协程通信：
   - 使用channel进行通信
   - 避免共享内存
   - 遵循CSP模型

4. 等待协程：
   - sync.WaitGroup：等待多个协程
   - channel：接收协程结果
   - context：处理取消和超时

5. 常见模式：
   - 生产者-消费者
   - 工作池
   - 扇入扇出
   - 管道

6. 注意事项：
   - 协程泄漏
   - 闭包变量捕获
   - 协程数量控制
   - 资源清理

7. 最佳实践：
   - 合理使用协程
   - 避免协程泄漏
   - 使用channel通信
   - 监控协程数量
   - 处理错误和超时

8. 性能优势：
   - 低内存开销
   - 快速创建和销毁
   - 高并发能力
   - 良好的调度性能
*/
