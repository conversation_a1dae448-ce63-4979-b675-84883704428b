// 04_data_types.go - Go语言基本数据类型
// 这个文件演示了Go语言中所有基本数据类型的使用

package main

import (
	"fmt"
	"unsafe"
)

func main() {
	fmt.Println("=== Go语言基本数据类型示例 ===")
	
	// 1. 布尔类型 (bool)
	fmt.Println("\n--- 布尔类型 ---")
	var isTrue bool = true
	var isFalse bool = false
	var defaultBool bool // 零值为false
	
	fmt.Printf("isTrue: %t\n", isTrue)         // 输出: isTrue: true
	fmt.Printf("isFalse: %t\n", isFalse)       // 输出: isFalse: false
	fmt.Printf("defaultBool: %t\n", defaultBool) // 输出: defaultBool: false
	
	// 2. 整数类型
	fmt.Println("\n--- 整数类型 ---")
	
	// 有符号整数
	var int8Var int8 = 127        // -128 到 127
	var int16Var int16 = 32767    // -32768 到 32767
	var int32Var int32 = 2147483647 // -2^31 到 2^31-1
	var int64Var int64 = 9223372036854775807 // -2^63 到 2^63-1
	var intVar int = 42           // 平台相关，32位或64位
	
	fmt.Printf("int8: %d (大小: %d字节)\n", int8Var, unsafe.Sizeof(int8Var))     // 输出: int8: 127 (大小: 1字节)
	fmt.Printf("int16: %d (大小: %d字节)\n", int16Var, unsafe.Sizeof(int16Var))   // 输出: int16: 32767 (大小: 2字节)
	fmt.Printf("int32: %d (大小: %d字节)\n", int32Var, unsafe.Sizeof(int32Var))   // 输出: int32: 2147483647 (大小: 4字节)
	fmt.Printf("int64: %d (大小: %d字节)\n", int64Var, unsafe.Sizeof(int64Var))   // 输出: int64: 9223372036854775807 (大小: 8字节)
	fmt.Printf("int: %d (大小: %d字节)\n", intVar, unsafe.Sizeof(intVar))         // 输出: int: 42 (大小: 8字节)
	
	// 无符号整数
	var uint8Var uint8 = 255      // 0 到 255
	var uint16Var uint16 = 65535  // 0 到 65535
	var uint32Var uint32 = 4294967295 // 0 到 2^32-1
	var uint64Var uint64 = 18446744073709551615 // 0 到 2^64-1
	var uintVar uint = 100        // 平台相关
	
	fmt.Printf("uint8: %d\n", uint8Var)   // 输出: uint8: 255
	fmt.Printf("uint16: %d\n", uint16Var) // 输出: uint16: 65535
	fmt.Printf("uint32: %d\n", uint32Var) // 输出: uint32: 4294967295
	fmt.Printf("uint64: %d\n", uint64Var) // 输出: uint64: 18446744073709551615
	fmt.Printf("uint: %d\n", uintVar)     // 输出: uint: 100
	
	// 特殊整数类型
	var byteVar byte = 255        // byte是uint8的别名
	var runeVar rune = '中'       // rune是int32的别名，用于Unicode字符
	var uintptrVar uintptr = 0x12345 // 用于存储指针的整数类型
	
	fmt.Printf("byte: %d (字符: %c)\n", byteVar, byteVar)     // 输出: byte: 255 (字符: ÿ)
	fmt.Printf("rune: %d (字符: %c)\n", runeVar, runeVar)     // 输出: rune: 20013 (字符: 中)
	fmt.Printf("uintptr: %d (十六进制: 0x%x)\n", uintptrVar, uintptrVar) // 输出: uintptr: 74565 (十六进制: 0x12345)
	
	// 3. 浮点数类型
	fmt.Println("\n--- 浮点数类型 ---")
	var float32Var float32 = 3.14159
	var float64Var float64 = 3.141592653589793
	
	fmt.Printf("float32: %.5f (大小: %d字节)\n", float32Var, unsafe.Sizeof(float32Var)) // 输出: float32: 3.14159 (大小: 4字节)
	fmt.Printf("float64: %.15f (大小: %d字节)\n", float64Var, unsafe.Sizeof(float64Var)) // 输出: float64: 3.141592653589793 (大小: 8字节)
	
	// 科学计数法
	var scientificFloat = 1.23e4  // 12300
	var smallFloat = 1.23e-4      // 0.000123
	
	fmt.Printf("科学计数法: %.0f, %.6f\n", scientificFloat, smallFloat) // 输出: 科学计数法: 12300, 0.000123
	
	// 4. 复数类型
	fmt.Println("\n--- 复数类型 ---")
	var complex64Var complex64 = 1 + 2i
	var complex128Var complex128 = 3 + 4i
	
	fmt.Printf("complex64: %v\n", complex64Var)   // 输出: complex64: (1+2i)
	fmt.Printf("complex128: %v\n", complex128Var) // 输出: complex128: (3+4i)
	
	// 复数操作
	real64 := real(complex64Var)    // 获取实部
	imag64 := imag(complex64Var)    // 获取虚部
	
	fmt.Printf("实部: %.1f, 虚部: %.1f\n", real64, imag64) // 输出: 实部: 1.0, 虚部: 2.0
	
	// 5. 字符串类型
	fmt.Println("\n--- 字符串类型 ---")
	var str1 string = "Hello, 世界!"
	var str2 = "Go语言"
	str3 := "学习编程"
	
	fmt.Printf("str1: %s (长度: %d字节)\n", str1, len(str1))   // 输出: str1: Hello, 世界! (长度: 13字节)
	fmt.Printf("str2: %s (长度: %d字节)\n", str2, len(str2))   // 输出: str2: Go语言 (长度: 8字节)
	fmt.Printf("str3: %s (长度: %d字节)\n", str3, len(str3))   // 输出: str3: 学习编程 (长度: 12字节)
	
	// 字符串操作
	concatenated := str2 + str3
	fmt.Printf("字符串连接: %s\n", concatenated) // 输出: 字符串连接: Go语言学习编程
	
	// 原始字符串字面量
	rawString := `这是一个
多行字符串
包含换行符`
	fmt.Printf("原始字符串:\n%s\n", rawString)
	
	// 6. 类型转换示例
	fmt.Println("\n--- 类型转换 ---")
	var intValue int = 42
	var floatValue float64 = float64(intValue)
	var stringValue string = fmt.Sprintf("%d", intValue)
	
	fmt.Printf("int转float64: %d -> %.1f\n", intValue, floatValue)     // 输出: int转float64: 42 -> 42.0
	fmt.Printf("int转string: %d -> %s\n", intValue, stringValue)       // 输出: int转string: 42 -> 42
	
	// 7. 零值总结
	fmt.Println("\n--- 零值总结 ---")
	var zeroInt int
	var zeroFloat float64
	var zeroString string
	var zeroBool bool
	var zeroComplex complex128
	
	fmt.Printf("int零值: %d\n", zeroInt)           // 输出: int零值: 0
	fmt.Printf("float64零值: %f\n", zeroFloat)     // 输出: float64零值: 0.000000
	fmt.Printf("string零值: '%s'\n", zeroString)   // 输出: string零值: ''
	fmt.Printf("bool零值: %t\n", zeroBool)         // 输出: bool零值: false
	fmt.Printf("complex128零值: %v\n", zeroComplex) // 输出: complex128零值: (0+0i)
}

/*
运行命令: go run 01_basics/04_data_types.go

Go语言基本数据类型总结：

1. 布尔类型：bool (true/false)

2. 整数类型：
   - 有符号：int8, int16, int32, int64, int
   - 无符号：uint8, uint16, uint32, uint64, uint
   - 别名：byte(uint8), rune(int32), uintptr

3. 浮点数类型：float32, float64

4. 复数类型：complex64, complex128

5. 字符串类型：string

类型转换规则：
- Go语言不支持隐式类型转换
- 必须显式进行类型转换：T(v)
- 只能在兼容的类型之间转换

内存大小：
- int, uint, uintptr的大小取决于平台（32位或64位）
- 其他类型的大小是固定的
- 使用unsafe.Sizeof()可以查看类型的内存大小
*/
