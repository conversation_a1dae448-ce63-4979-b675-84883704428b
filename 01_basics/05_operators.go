// 05_operators.go - Go语言运算符
// 这个文件演示了Go语言中所有运算符的使用

package main

import "fmt"

func main() {
	fmt.Println("=== Go语言运算符示例 ===")
	
	// 1. 算术运算符
	fmt.Println("\n--- 算术运算符 ---")
	a, b := 10, 3
	
	fmt.Printf("a = %d, b = %d\n", a, b)                    // 输出: a = 10, b = 3
	fmt.Printf("a + b = %d\n", a+b)                         // 输出: a + b = 13
	fmt.Printf("a - b = %d\n", a-b)                         // 输出: a - b = 7
	fmt.Printf("a * b = %d\n", a*b)                         // 输出: a * b = 30
	fmt.Printf("a / b = %d\n", a/b)                         // 输出: a / b = 3 (整数除法)
	fmt.Printf("a %% b = %d\n", a%b)                        // 输出: a % b = 1 (取余)
	
	// 浮点数除法
	x, y := 10.0, 3.0
	fmt.Printf("%.1f / %.1f = %.3f\n", x, y, x/y)          // 输出: 10.0 / 3.0 = 3.333
	
	// 2. 赋值运算符
	fmt.Println("\n--- 赋值运算符 ---")
	num := 10
	fmt.Printf("初始值: num = %d\n", num)                   // 输出: 初始值: num = 10
	
	num += 5  // num = num + 5
	fmt.Printf("num += 5: num = %d\n", num)                 // 输出: num += 5: num = 15
	
	num -= 3  // num = num - 3
	fmt.Printf("num -= 3: num = %d\n", num)                 // 输出: num -= 3: num = 12
	
	num *= 2  // num = num * 2
	fmt.Printf("num *= 2: num = %d\n", num)                 // 输出: num *= 2: num = 24
	
	num /= 4  // num = num / 4
	fmt.Printf("num /= 4: num = %d\n", num)                 // 输出: num /= 4: num = 6
	
	num %= 4  // num = num % 4
	fmt.Printf("num %%= 4: num = %d\n", num)                // 输出: num %= 4: num = 2
	
	// 3. 比较运算符
	fmt.Println("\n--- 比较运算符 ---")
	p, q := 5, 8
	
	fmt.Printf("p = %d, q = %d\n", p, q)                    // 输出: p = 5, q = 8
	fmt.Printf("p == q: %t\n", p == q)                      // 输出: p == q: false
	fmt.Printf("p != q: %t\n", p != q)                      // 输出: p != q: true
	fmt.Printf("p < q: %t\n", p < q)                        // 输出: p < q: true
	fmt.Printf("p <= q: %t\n", p <= q)                      // 输出: p <= q: true
	fmt.Printf("p > q: %t\n", p > q)                        // 输出: p > q: false
	fmt.Printf("p >= q: %t\n", p >= q)                      // 输出: p >= q: false
	
	// 字符串比较
	str1, str2 := "apple", "banana"
	fmt.Printf("\"%s\" < \"%s\": %t\n", str1, str2, str1 < str2) // 输出: "apple" < "banana": true
	
	// 4. 逻辑运算符
	fmt.Println("\n--- 逻辑运算符 ---")
	isTrue, isFalse := true, false
	
	fmt.Printf("isTrue = %t, isFalse = %t\n", isTrue, isFalse)     // 输出: isTrue = true, isFalse = false
	fmt.Printf("isTrue && isFalse: %t\n", isTrue && isFalse)       // 输出: isTrue && isFalse: false (逻辑与)
	fmt.Printf("isTrue || isFalse: %t\n", isTrue || isFalse)       // 输出: isTrue || isFalse: true (逻辑或)
	fmt.Printf("!isTrue: %t\n", !isTrue)                           // 输出: !isTrue: false (逻辑非)
	fmt.Printf("!isFalse: %t\n", !isFalse)                         // 输出: !isFalse: true (逻辑非)
	
	// 短路求值示例
	fmt.Println("\n短路求值示例:")
	result1 := false && (10/0 == 1) // 不会执行除零操作，因为false && 任何值 = false
	fmt.Printf("false && (10/0 == 1): %t\n", result1)             // 输出: false && (10/0 == 1): false
	
	result2 := true || (10/0 == 1)  // 不会执行除零操作，因为true || 任何值 = true
	fmt.Printf("true || (10/0 == 1): %t\n", result2)              // 输出: true || (10/0 == 1): true
	
	// 5. 位运算符
	fmt.Println("\n--- 位运算符 ---")
	m, n := 12, 10  // 12 = 1100(二进制), 10 = 1010(二进制)
	
	fmt.Printf("m = %d (二进制: %04b)\n", m, m)                    // 输出: m = 12 (二进制: 1100)
	fmt.Printf("n = %d (二进制: %04b)\n", n, n)                    // 输出: n = 10 (二进制: 1010)
	fmt.Printf("m & n = %d (二进制: %04b)\n", m&n, m&n)           // 输出: m & n = 8 (二进制: 1000) 按位与
	fmt.Printf("m | n = %d (二进制: %04b)\n", m|n, m|n)           // 输出: m | n = 14 (二进制: 1110) 按位或
	fmt.Printf("m ^ n = %d (二进制: %04b)\n", m^n, m^n)           // 输出: m ^ n = 6 (二进制: 0110) 按位异或
	fmt.Printf("^m = %d\n", ^m)                                    // 输出: ^m = -13 (按位取反)
	
	// 位移运算符
	fmt.Printf("m << 2 = %d (二进制: %b)\n", m<<2, m<<2)          // 输出: m << 2 = 48 (二进制: 110000) 左移
	fmt.Printf("m >> 2 = %d (二进制: %b)\n", m>>2, m>>2)          // 输出: m >> 2 = 3 (二进制: 11) 右移
	
	// 6. 自增自减运算符
	fmt.Println("\n--- 自增自减运算符 ---")
	counter := 5
	fmt.Printf("初始值: counter = %d\n", counter)                  // 输出: 初始值: counter = 5
	
	counter++  // 等价于 counter = counter + 1
	fmt.Printf("counter++: counter = %d\n", counter)               // 输出: counter++: counter = 6
	
	counter--  // 等价于 counter = counter - 1
	fmt.Printf("counter--: counter = %d\n", counter)               // 输出: counter--: counter = 5
	
	// 注意：Go语言中++和--是语句，不是表达式，不能用于赋值
	// 错误示例：result := counter++  // 这会导致编译错误
	
	// 7. 指针运算符
	fmt.Println("\n--- 指针运算符 ---")
	value := 42
	ptr := &value  // &取地址运算符
	
	fmt.Printf("value = %d\n", value)                              // 输出: value = 42
	fmt.Printf("ptr = %p\n", ptr)                                  // 输出: ptr = 0x... (内存地址)
	fmt.Printf("*ptr = %d\n", *ptr)                                // 输出: *ptr = 42 (*解引用运算符)
	
	*ptr = 100  // 通过指针修改值
	fmt.Printf("修改后 value = %d\n", value)                       // 输出: 修改后 value = 100
	
	// 8. 运算符优先级示例
	fmt.Println("\n--- 运算符优先级 ---")
	result := 2 + 3 * 4    // 乘法优先级高于加法
	fmt.Printf("2 + 3 * 4 = %d\n", result)                        // 输出: 2 + 3 * 4 = 14
	
	result = (2 + 3) * 4   // 括号改变优先级
	fmt.Printf("(2 + 3) * 4 = %d\n", result)                      // 输出: (2 + 3) * 4 = 20
	
	// 复杂表达式
	complex := 10 + 5*2 - 8/4 + 1
	fmt.Printf("10 + 5*2 - 8/4 + 1 = %d\n", complex)             // 输出: 10 + 5*2 - 8/4 + 1 = 19
	
	// 9. 类型断言运算符（接口相关，后续章节详细介绍）
	fmt.Println("\n--- 其他运算符 ---")
	var i interface{} = "hello"
	str, ok := i.(string)  // 类型断言
	fmt.Printf("类型断言: %s, %t\n", str, ok)                      // 输出: 类型断言: hello, true
	
	fmt.Println("\n=== 运算符优先级（从高到低）===")
	fmt.Println("1. * / % << >> & &^")
	fmt.Println("2. + - | ^")
	fmt.Println("3. == != < <= > >=")
	fmt.Println("4. &&")
	fmt.Println("5. ||")
}

/*
运行命令: go run 01_basics/05_operators.go

Go语言运算符总结：

1. 算术运算符：+ - * / %
2. 赋值运算符：= += -= *= /= %=
3. 比较运算符：== != < <= > >=
4. 逻辑运算符：&& || !
5. 位运算符：& | ^ << >> &^
6. 自增自减：++ --
7. 指针运算符：& *
8. 其他：类型断言 .(type)

特殊说明：
- Go语言中++和--是语句，不是表达式
- 除法运算：整数除法结果为整数，浮点数除法结果为浮点数
- 位运算符只能用于整数类型
- 逻辑运算符支持短路求值
- 字符串可以使用比较运算符进行字典序比较
*/
