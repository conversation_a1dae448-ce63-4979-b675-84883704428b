// 02_variables.go - Go语言变量声明和使用
// 这个文件演示了Go语言中各种变量声明和初始化的方法

package main

import "fmt"

func main() {
	fmt.Println("=== Go语言变量声明示例 ===")
	
	// 1. 使用var关键字声明变量（完整形式）
	var name string = "张三"
	var age int = 25
	var height float64 = 175.5
	var isStudent bool = true
	
	fmt.Printf("姓名: %s, 年龄: %d, 身高: %.1f, 是否学生: %t\n", 
		name, age, height, isStudent) // 输出: 姓名: 张三, 年龄: 25, 身高: 175.5, 是否学生: true
	
	// 2. 类型推断（省略类型）
	var city = "北京"        // Go会自动推断为string类型
	var population = 2000   // Go会自动推断为int类型
	var temperature = 23.5  // Go会自动推断为float64类型
	
	fmt.Printf("城市: %s, 人口: %d万, 温度: %.1f°C\n", 
		city, population, temperature) // 输出: 城市: 北京, 人口: 2000万, 温度: 23.5°C
	
	// 3. 短变量声明（最常用的方式）
	country := "中国"
	year := 2024
	gdp := 17.7
	
	fmt.Printf("国家: %s, 年份: %d, GDP: %.1f万亿美元\n", 
		country, year, gdp) // 输出: 国家: 中国, 年份: 2024, GDP: 17.7万亿美元
	
	// 4. 多变量声明
	var x, y, z int = 1, 2, 3
	fmt.Printf("x=%d, y=%d, z=%d\n", x, y, z) // 输出: x=1, y=2, z=3
	
	// 5. 多变量声明（类型推断）
	var a, b, c = 10, "hello", true
	fmt.Printf("a=%d, b=%s, c=%t\n", a, b, c) // 输出: a=10, b=hello, c=true
	
	// 6. 多变量短声明
	m, n, o := 100, 200, 300
	fmt.Printf("m=%d, n=%d, o=%d\n", m, n, o) // 输出: m=100, n=200, o=300
	
	// 7. 变量的零值（默认值）
	var defaultInt int
	var defaultFloat float64
	var defaultString string
	var defaultBool bool
	
	fmt.Printf("int零值: %d\n", defaultInt)         // 输出: int零值: 0
	fmt.Printf("float64零值: %f\n", defaultFloat)   // 输出: float64零值: 0.000000
	fmt.Printf("string零值: '%s'\n", defaultString) // 输出: string零值: ''
	fmt.Printf("bool零值: %t\n", defaultBool)       // 输出: bool零值: false
	
	// 8. 变量重新赋值
	name = "李四"
	age = 30
	fmt.Printf("更新后 - 姓名: %s, 年龄: %d\n", name, age) // 输出: 更新后 - 姓名: 李四, 年龄: 30
	
	// 9. 变量作用域示例
	{
		// 这是一个新的作用域
		localVar := "我是局部变量"
		fmt.Println(localVar) // 输出: 我是局部变量
		
		// 可以访问外部作用域的变量
		fmt.Printf("外部变量name: %s\n", name) // 输出: 外部变量name: 李四
	}
	// localVar在这里不可访问
	
	fmt.Println("=== 变量声明总结 ===")
	fmt.Println("1. var name type = value  // 完整声明")
	fmt.Println("2. var name = value       // 类型推断")
	fmt.Println("3. name := value          // 短声明（最常用）")
	fmt.Println("4. var a, b, c = 1, 2, 3  // 多变量声明")
}

/*
运行命令: go run 01_basics/02_variables.go

变量声明规则：
1. 变量名必须以字母或下划线开头
2. 变量名区分大小写
3. 不能使用Go语言的关键字
4. 短声明(:=)只能在函数内部使用
5. 已声明的变量必须被使用，否则编译错误

Go语言的零值：
- 数值类型：0
- 布尔类型：false
- 字符串类型：""（空字符串）
- 指针、切片、映射、通道、函数、接口：nil
*/
