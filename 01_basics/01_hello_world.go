// 01_hello_world.go - Go语言的第一个程序
// 这个文件演示了Go程序的基本结构和Hello World程序

package main // 每个Go程序都必须属于一个包，main包是程序的入口点

import "fmt" // 导入fmt包，用于格式化输入输出

// main函数是程序的入口点，程序从这里开始执行
func main() {
	// 使用fmt.Println()函数打印文本到控制台
	fmt.Println("Hello, World!") // 输出: Hello, World!
	
	// 使用fmt.Print()函数打印文本（不换行）
	fmt.Print("Hello, ") // 输出: Hello, 
	fmt.Print("Go!")     // 输出: Go!
	fmt.Println()        // 输出一个换行符
	
	// 使用fmt.Printf()函数进行格式化输出
	fmt.Printf("Hello, %s!\n", "Golang") // 输出: Hello, Golang!
	
	// Go程序的基本结构说明：
	// 1. package声明：定义当前文件属于哪个包
	// 2. import语句：导入需要使用的包
	// 3. 函数、变量、常量、类型的声明
	
	fmt.Println("欢迎来到Go语言的世界！") // 输出: 欢迎来到Go语言的世界！
}

/*
程序运行方法：
1. 在终端中进入项目目录
2. 激活conda环境: conda activate golang
3. 运行命令: go run 01_basics/01_hello_world.go

预期输出：
Hello, World!
Hello, Go!
Hello, Golang!
欢迎来到Go语言的世界！

Go语言特点：
- 静态类型语言
- 编译型语言
- 支持并发编程
- 语法简洁
- 内存管理自动化（垃圾回收）
- 跨平台编译
*/
