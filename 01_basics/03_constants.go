// 03_constants.go - Go语言常量声明和使用
// 这个文件演示了Go语言中常量的各种声明方式和特性

package main

import "fmt"

// 包级别常量声明
const (
	CompanyName = "科技有限公司"
	Version     = "1.0.0"
	MaxUsers    = 1000
)

// 使用iota创建枚举常量
const (
	Sunday = iota    // 0
	Monday           // 1
	Tuesday          // 2
	Wednesday        // 3
	Thursday         // 4
	Friday           // 5
	Saturday         // 6
)

// 文件大小常量（使用iota进行位运算）
const (
	_  = iota             // 忽略第一个值
	KB = 1 << (10 * iota) // 1 << (10*1) = 1024
	MB                    // 1 << (10*2) = 1048576
	GB                    // 1 << (10*3) = 1073741824
	TB                    // 1 << (10*4) = 1099511627776
)

func main() {
	fmt.Println("=== Go语言常量示例 ===")
	
	// 1. 基本常量声明
	const pi = 3.14159
	const greeting = "你好，世界！"
	const isDebug = true
	
	fmt.Printf("圆周率: %.5f\n", pi)           // 输出: 圆周率: 3.14159
	fmt.Printf("问候语: %s\n", greeting)       // 输出: 问候语: 你好，世界！
	fmt.Printf("调试模式: %t\n", isDebug)      // 输出: 调试模式: true
	
	// 2. 类型化常量
	const typedInt int = 42
	const typedFloat float64 = 3.14
	const typedString string = "Go语言"
	
	fmt.Printf("类型化整数: %d (类型: %T)\n", typedInt, typedInt)       // 输出: 类型化整数: 42 (类型: int)
	fmt.Printf("类型化浮点数: %.2f (类型: %T)\n", typedFloat, typedFloat) // 输出: 类型化浮点数: 3.14 (类型: float64)
	fmt.Printf("类型化字符串: %s (类型: %T)\n", typedString, typedString) // 输出: 类型化字符串: Go语言 (类型: string)
	
	// 3. 多常量声明
	const (
		name   = "张三"
		age    = 25
		salary = 8000.50
	)
	
	fmt.Printf("员工信息 - 姓名: %s, 年龄: %d, 薪资: %.2f\n", 
		name, age, salary) // 输出: 员工信息 - 姓名: 张三, 年龄: 25, 薪资: 8000.50
	
	// 4. 使用包级别常量
	fmt.Printf("公司: %s, 版本: %s, 最大用户数: %d\n", 
		CompanyName, Version, MaxUsers) // 输出: 公司: 科技有限公司, 版本: 1.0.0, 最大用户数: 1000
	
	// 5. 枚举常量示例
	fmt.Println("\n=== 星期枚举 ===")
	fmt.Printf("星期日: %d\n", Sunday)    // 输出: 星期日: 0
	fmt.Printf("星期一: %d\n", Monday)    // 输出: 星期一: 1
	fmt.Printf("星期二: %d\n", Tuesday)   // 输出: 星期二: 2
	fmt.Printf("星期三: %d\n", Wednesday) // 输出: 星期三: 3
	fmt.Printf("星期四: %d\n", Thursday)  // 输出: 星期四: 4
	fmt.Printf("星期五: %d\n", Friday)    // 输出: 星期五: 5
	fmt.Printf("星期六: %d\n", Saturday)  // 输出: 星期六: 6
	
	// 6. 文件大小常量示例
	fmt.Println("\n=== 文件大小常量 ===")
	fmt.Printf("1 KB = %d 字节\n", KB) // 输出: 1 KB = 1024 字节
	fmt.Printf("1 MB = %d 字节\n", MB) // 输出: 1 MB = 1048576 字节
	fmt.Printf("1 GB = %d 字节\n", GB) // 输出: 1 GB = 1073741824 字节
	fmt.Printf("1 TB = %d 字节\n", TB) // 输出: 1 TB = 1099511627776 字节
	
	// 7. 常量表达式
	const (
		a = 10
		b = 20
		c = a + b    // 常量表达式
		d = a * b    // 常量表达式
		e = b / a    // 常量表达式
	)
	
	fmt.Println("\n=== 常量表达式 ===")
	fmt.Printf("a = %d, b = %d\n", a, b)     // 输出: a = 10, b = 20
	fmt.Printf("c = a + b = %d\n", c)        // 输出: c = a + b = 30
	fmt.Printf("d = a * b = %d\n", d)        // 输出: d = a * b = 200
	fmt.Printf("e = b / a = %d\n", e)        // 输出: e = b / a = 2
	
	// 8. 无类型常量的灵活性
	const flexibleConst = 42
	
	var intVar int = flexibleConst
	var floatVar float64 = flexibleConst
	var complexVar complex128 = flexibleConst
	
	fmt.Println("\n=== 无类型常量的灵活性 ===")
	fmt.Printf("整数变量: %d\n", intVar)         // 输出: 整数变量: 42
	fmt.Printf("浮点变量: %.1f\n", floatVar)     // 输出: 浮点变量: 42.0
	fmt.Printf("复数变量: %.1f\n", complexVar)   // 输出: 复数变量: (42.0+0.0i)
	
	fmt.Println("\n=== 常量特性总结 ===")
	fmt.Println("1. 常量在编译时确定值")
	fmt.Println("2. 常量不能被重新赋值")
	fmt.Println("3. 常量可以是无类型的，提供更大的灵活性")
	fmt.Println("4. iota用于创建枚举常量")
	fmt.Println("5. 常量表达式在编译时计算")
}

/*
运行命令: go run 01_basics/03_constants.go

常量的特点：
1. 常量的值在编译时确定，运行时不能改变
2. 常量可以是数值、布尔值或字符串类型
3. 常量可以是有类型的或无类型的
4. 无类型常量具有更高的精度和灵活性
5. iota是常量生成器，用于生成一组相关的常量

iota的特性：
- iota在每个const关键字出现时重置为0
- iota在每一行常量声明中递增1
- 可以用iota进行复杂的常量计算
- 可以使用_忽略不需要的iota值

常量 vs 变量：
- 常量：编译时确定，不可修改，使用const声明
- 变量：运行时可修改，使用var或:=声明
*/
