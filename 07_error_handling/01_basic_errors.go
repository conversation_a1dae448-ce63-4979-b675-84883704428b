// 01_basic_errors.go - Go语言基础错误处理
// 这个文件演示了Go语言中基础错误处理的概念和使用

package main

import (
	"errors"
	"fmt"
	"strconv"
)

// 1. 基本错误处理
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, errors.New("除数不能为零")
	}
	return a / b, nil
}

// 2. 使用fmt.Errorf创建格式化错误
func validateAge(age int) error {
	if age < 0 {
		return fmt.Errorf("年龄不能为负数，得到: %d", age)
	}
	if age > 150 {
		return fmt.Errorf("年龄不能超过150岁，得到: %d", age)
	}
	return nil
}

// 3. 自定义错误类型
type ValidationError struct {
	Field   string
	Value   interface{}
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("验证错误 - 字段: %s, 值: %v, 消息: %s", e.<PERSON>, e.Value, e.Message)
}

// 用户结构体
type User struct {
	Name  string
	Email string
	Age   int
}

// 验证用户信息
func validateUser(user User) error {
	if user.Name == "" {
		return ValidationError{
			Field:   "Name",
			Value:   user.Name,
			Message: "姓名不能为空",
		}
	}
	
	if len(user.Name) < 2 {
		return ValidationError{
			Field:   "Name",
			Value:   user.Name,
			Message: "姓名长度不能少于2个字符",
		}
	}
	
	if user.Email == "" {
		return ValidationError{
			Field:   "Email",
			Value:   user.Email,
			Message: "邮箱不能为空",
		}
	}
	
	if user.Age < 0 || user.Age > 150 {
		return ValidationError{
			Field:   "Age",
			Value:   user.Age,
			Message: "年龄必须在0-150之间",
		}
	}
	
	return nil
}

// 4. 错误包装和展开
func processData(data string) error {
	// 尝试转换为整数
	num, err := strconv.Atoi(data)
	if err != nil {
		return fmt.Errorf("处理数据失败: %w", err) // 包装错误
	}
	
	// 验证数值范围
	if num < 0 {
		return fmt.Errorf("数值验证失败，数值不能为负: %d", num)
	}
	
	if num > 1000 {
		return fmt.Errorf("数值验证失败，数值不能超过1000: %d", num)
	}
	
	fmt.Printf("数据处理成功: %d\n", num)
	return nil
}

// 5. 多个错误的处理
type MultiError struct {
	Errors []error
}

func (me MultiError) Error() string {
	if len(me.Errors) == 0 {
		return "没有错误"
	}
	
	result := fmt.Sprintf("发生了 %d 个错误:\n", len(me.Errors))
	for i, err := range me.Errors {
		result += fmt.Sprintf("  %d. %s\n", i+1, err.Error())
	}
	return result
}

func validateMultipleUsers(users []User) error {
	var errors []error
	
	for i, user := range users {
		if err := validateUser(user); err != nil {
			wrappedErr := fmt.Errorf("用户 %d 验证失败: %w", i+1, err)
			errors = append(errors, wrappedErr)
		}
	}
	
	if len(errors) > 0 {
		return MultiError{Errors: errors}
	}
	
	return nil
}

// 6. 错误处理的辅助函数
func must(value interface{}, err error) interface{} {
	if err != nil {
		panic(err)
	}
	return value
}

func ignore(value interface{}, err error) interface{} {
	return value
}

// 7. 文件操作错误处理示例
func readConfig(filename string) (map[string]string, error) {
	// 模拟文件读取
	if filename == "" {
		return nil, errors.New("文件名不能为空")
	}
	
	if filename == "notfound.txt" {
		return nil, fmt.Errorf("文件不存在: %s", filename)
	}
	
	if filename == "permission.txt" {
		return nil, fmt.Errorf("没有权限读取文件: %s", filename)
	}
	
	// 模拟成功读取
	config := map[string]string{
		"host":     "localhost",
		"port":     "8080",
		"database": "mydb",
	}
	
	return config, nil
}

// 8. 链式错误处理
func processConfig(filename string) error {
	config, err := readConfig(filename)
	if err != nil {
		return fmt.Errorf("读取配置失败: %w", err)
	}
	
	host, exists := config["host"]
	if !exists {
		return errors.New("配置中缺少host字段")
	}
	
	if host == "" {
		return errors.New("host字段不能为空")
	}
	
	fmt.Printf("配置处理成功，主机: %s\n", host)
	return nil
}

func main() {
	fmt.Println("=== Go语言基础错误处理示例 ===")
	
	// 1. 基本错误处理
	fmt.Println("\n--- 基本错误处理 ---")
	
	result, err := divide(10, 2)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("10 ÷ 2 = %.2f\n", result) // 输出: 10 ÷ 2 = 5.00
	}
	
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("错误: %v\n", err) // 输出: 错误: 除数不能为零
	} else {
		fmt.Printf("结果: %.2f\n", result)
	}
	
	// 2. 格式化错误
	fmt.Println("\n--- 格式化错误 ---")
	
	ages := []int{25, -5, 200, 30}
	for _, age := range ages {
		if err := validateAge(age); err != nil {
			fmt.Printf("年龄验证失败: %v\n", err)
		} else {
			fmt.Printf("年龄 %d 验证通过\n", age)
		}
	}
	
	// 3. 自定义错误类型
	fmt.Println("\n--- 自定义错误类型 ---")
	
	users := []User{
		{Name: "张三", Email: "<EMAIL>", Age: 25},
		{Name: "", Email: "<EMAIL>", Age: 30},
		{Name: "李", Email: "", Age: 20},
		{Name: "王五", Email: "<EMAIL>", Age: -5},
	}
	
	for i, user := range users {
		if err := validateUser(user); err != nil {
			fmt.Printf("用户 %d 验证失败: %v\n", i+1, err)
			
			// 类型断言检查具体错误类型
			if validationErr, ok := err.(ValidationError); ok {
				fmt.Printf("  详细信息 - 字段: %s, 值: %v\n", validationErr.Field, validationErr.Value)
			}
		} else {
			fmt.Printf("用户 %d 验证通过: %s\n", i+1, user.Name)
		}
	}
	
	// 4. 错误包装和展开
	fmt.Println("\n--- 错误包装和展开 ---")
	
	testData := []string{"123", "abc", "-456", "2000"}
	
	for _, data := range testData {
		if err := processData(data); err != nil {
			fmt.Printf("处理 '%s' 失败: %v\n", data, err)
			
			// 使用errors.Unwrap展开错误
			if unwrapped := errors.Unwrap(err); unwrapped != nil {
				fmt.Printf("  原始错误: %v\n", unwrapped)
			}
			
			// 使用errors.Is检查错误类型
			var numErr *strconv.NumError
			if errors.As(err, &numErr) {
				fmt.Printf("  数值转换错误: %s\n", numErr.Num)
			}
		}
	}
	
	// 5. 多个错误处理
	fmt.Println("\n--- 多个错误处理 ---")
	
	invalidUsers := []User{
		{Name: "", Email: "", Age: -1},
		{Name: "A", Email: "invalid", Age: 200},
		{Name: "正常用户", Email: "<EMAIL>", Age: 25},
	}
	
	if err := validateMultipleUsers(invalidUsers); err != nil {
		fmt.Printf("批量验证失败:\n%v", err)
	} else {
		fmt.Println("所有用户验证通过")
	}
	
	// 6. 错误处理辅助函数
	fmt.Println("\n--- 错误处理辅助函数 ---")
	
	// 使用must函数（注意：这会在错误时panic）
	fmt.Println("使用must函数:")
	safeValue := must(divide(20, 4))
	fmt.Printf("安全值: %.2f\n", safeValue) // 输出: 安全值: 5.00
	
	// 使用ignore函数忽略错误
	fmt.Println("使用ignore函数:")
	ignoredValue := ignore(divide(10, 0))
	if ignoredValue != nil {
		fmt.Printf("忽略错误的值: %.2f\n", ignoredValue.(float64))
	} else {
		fmt.Println("忽略了错误，得到nil值")
	}
	
	// 7. 文件操作错误处理
	fmt.Println("\n--- 文件操作错误处理 ---")
	
	filenames := []string{"config.txt", "", "notfound.txt", "permission.txt"}
	
	for _, filename := range filenames {
		config, err := readConfig(filename)
		if err != nil {
			fmt.Printf("读取 '%s' 失败: %v\n", filename, err)
		} else {
			fmt.Printf("读取 '%s' 成功: %v\n", filename, config)
		}
	}
	
	// 8. 链式错误处理
	fmt.Println("\n--- 链式错误处理 ---")
	
	configFiles := []string{"config.txt", "notfound.txt", "permission.txt"}
	
	for _, filename := range configFiles {
		if err := processConfig(filename); err != nil {
			fmt.Printf("处理配置 '%s' 失败: %v\n", filename, err)
		}
	}
	
	// 9. 错误处理最佳实践
	fmt.Println("\n--- 错误处理最佳实践 ---")
	
	// 示例：数据库操作的错误处理模式
	connectDB := func() error {
		// 模拟数据库连接
		return errors.New("连接数据库失败")
	}
	
	queryData := func() ([]string, error) {
		if err := connectDB(); err != nil {
			return nil, fmt.Errorf("查询数据失败: %w", err)
		}
		return []string{"data1", "data2"}, nil
	}
	
	// 使用错误处理
	data, err := queryData()
	if err != nil {
		fmt.Printf("操作失败: %v\n", err)
		
		// 根据错误类型采取不同行动
		if errors.Is(err, errors.New("连接数据库失败")) {
			fmt.Println("建议: 检查数据库连接配置")
		}
	} else {
		fmt.Printf("查询成功: %v\n", data)
	}
	
	// 10. 错误处理原则总结
	fmt.Println("\n--- 错误处理原则 ---")
	
	fmt.Println("Go语言错误处理最佳实践:")
	fmt.Println("1. 错误是值，应该被检查和处理")
	fmt.Println("2. 使用多返回值，错误作为最后一个返回值")
	fmt.Println("3. 立即检查错误，不要忽略")
	fmt.Println("4. 使用fmt.Errorf包装错误，提供上下文")
	fmt.Println("5. 自定义错误类型提供更多信息")
	fmt.Println("6. 使用errors.Is和errors.As检查错误类型")
	fmt.Println("7. 在适当的层级处理错误")
	fmt.Println("8. 记录错误日志，但不要重复记录")
	fmt.Println("9. 优雅地处理错误，提供用户友好的消息")
	fmt.Println("10. 使用panic只用于真正的程序错误")
}

/*
运行命令: conda activate golang && go run 07_error_handling/01_basic_errors.go

Go语言基础错误处理总结：

1. 错误接口：
   - error是内置接口类型
   - 只有一个Error() string方法
   - nil表示没有错误

2. 创建错误：
   - errors.New("message")：简单错误
   - fmt.Errorf("format", args...)：格式化错误
   - 自定义错误类型：实现Error()方法

3. 错误处理模式：
   - 多返回值：(result, error)
   - 立即检查：if err != nil
   - 错误传播：向上返回错误

4. 错误包装：
   - fmt.Errorf("context: %w", err)：包装错误
   - errors.Unwrap(err)：展开错误
   - errors.Is(err, target)：检查错误类型
   - errors.As(err, &target)：类型断言

5. 自定义错误：
   - 实现error接口
   - 提供更多上下文信息
   - 支持类型断言

6. 错误处理策略：
   - 立即处理：在发生处处理
   - 向上传播：返回给调用者
   - 包装传播：添加上下文后传播
   - 忽略错误：确定安全时可忽略

7. 最佳实践：
   - 不要忽略错误
   - 提供有意义的错误消息
   - 在适当层级处理错误
   - 使用错误包装提供上下文
   - 避免重复记录同一错误

8. 注意事项：
   - 错误是值，可以被检查和操作
   - panic用于程序错误，不是异常处理
   - 错误处理是Go语言的核心特性
   - 明确的错误处理使代码更可靠
*/
