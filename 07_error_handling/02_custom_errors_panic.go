// 02_custom_errors_panic.go - Go语言自定义错误和panic/recover
// 这个文件演示了自定义错误类型和panic/recover机制的使用

package main

import (
	"fmt"
	"runtime"
	"time"
)

// 1. 简单自定义错误
type DivisionByZeroError struct {
	Dividend float64
}

func (e DivisionByZeroError) Error() string {
	return fmt.Sprintf("除零错误: 试图将 %.2f 除以零", e.Dividend)
}

// 2. 复杂自定义错误
type NetworkError struct {
	Op        string    // 操作类型
	URL       string    // 请求URL
	Timestamp time.Time // 错误发生时间
	Retry     int       // 重试次数
	Cause     error     // 原始错误
}

func (e NetworkError) Error() string {
	return fmt.Sprintf("网络错误 [%s] %s at %s (重试: %d次): %v",
		e.Op, e.URL, e.Timestamp.Format("15:04:05"), e.Retry, e.Cause)
}

func (e NetworkError) Unwrap() error {
	return e.Cause
}

// 3. 错误类型枚举
type ErrorCode int

const (
	ErrCodeUnknown ErrorCode = iota
	ErrCodeInvalidInput
	ErrCodeNotFound
	ErrCodePermissionDenied
	ErrCodeTimeout
	ErrCodeInternalError
)

func (ec ErrorCode) String() string {
	switch ec {
	case ErrCodeInvalidInput:
		return "INVALID_INPUT"
	case ErrCodeNotFound:
		return "NOT_FOUND"
	case ErrCodePermissionDenied:
		return "PERMISSION_DENIED"
	case ErrCodeTimeout:
		return "TIMEOUT"
	case ErrCodeInternalError:
		return "INTERNAL_ERROR"
	default:
		return "UNKNOWN"
	}
}

type ApplicationError struct {
	Code    ErrorCode
	Message string
	Details map[string]interface{}
}

func (e ApplicationError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 4. 错误工厂函数
func NewValidationError(field, value string) ApplicationError {
	return ApplicationError{
		Code:    ErrCodeInvalidInput,
		Message: fmt.Sprintf("字段验证失败: %s", field),
		Details: map[string]interface{}{
			"field": field,
			"value": value,
		},
	}
}

func NewNotFoundError(resource, id string) ApplicationError {
	return ApplicationError{
		Code:    ErrCodeNotFound,
		Message: fmt.Sprintf("资源未找到: %s", resource),
		Details: map[string]interface{}{
			"resource": resource,
			"id":       id,
		},
	}
}

// 5. 业务逻辑函数
func safeDivide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, DivisionByZeroError{Dividend: a}
	}
	return a / b, nil
}

func fetchUserData(userID string) (map[string]interface{}, error) {
	// 模拟不同的错误情况
	switch userID {
	case "":
		return nil, NewValidationError("userID", userID)
	case "404":
		return nil, NewNotFoundError("user", userID)
	case "timeout":
		return nil, ApplicationError{
			Code:    ErrCodeTimeout,
			Message: "请求超时",
			Details: map[string]interface{}{
				"timeout": "5s",
				"userID":  userID,
			},
		}
	case "network":
		return nil, NetworkError{
			Op:        "GET",
			URL:       fmt.Sprintf("/api/users/%s", userID),
			Timestamp: time.Now(),
			Retry:     3,
			Cause:     fmt.Errorf("连接被拒绝"),
		}
	default:
		return map[string]interface{}{
			"id":   userID,
			"name": "用户" + userID,
			"age":  25,
		}, nil
	}
}

// 6. Panic和Recover基础示例
func demonstratePanic() {
	fmt.Println("--- Panic和Recover基础示例 ---")
	
	// 使用defer和recover捕获panic
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("捕获到panic: %v\n", r)
			
			// 获取调用栈信息
			buf := make([]byte, 1024)
			n := runtime.Stack(buf, false)
			fmt.Printf("调用栈:\n%s", buf[:n])
		}
	}()
	
	fmt.Println("正常执行...")
	
	// 触发panic
	panic("这是一个测试panic")
	
	fmt.Println("这行代码不会执行") // 不会执行
}

// 7. 安全的数组访问
func safeArrayAccess(arr []int, index int) (value int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("数组访问越界: index=%d, length=%d", index, len(arr))
		}
	}()
	
	value = arr[index] // 可能引发panic
	return value, nil
}

// 8. 资源清理与panic
func resourceCleanupDemo() {
	fmt.Println("\n--- 资源清理与panic ---")
	
	// 模拟资源
	resource := "数据库连接"
	
	defer func() {
		fmt.Printf("清理资源: %s\n", resource)
		
		if r := recover(); r != nil {
			fmt.Printf("在资源清理时捕获panic: %v\n", r)
		}
	}()
	
	fmt.Printf("获取资源: %s\n", resource)
	
	// 模拟可能出错的操作
	fmt.Println("执行可能出错的操作...")
	
	// 模拟panic
	if true { // 条件控制是否panic
		panic("操作失败")
	}
	
	fmt.Println("操作成功完成")
}

// 9. 错误恢复和重试机制
func retryWithRecovery(operation func() error, maxRetries int) error {
	var lastErr error
	
	for i := 0; i < maxRetries; i++ {
		func() {
			defer func() {
				if r := recover(); r != nil {
					lastErr = fmt.Errorf("操作panic: %v", r)
				}
			}()
			
			lastErr = operation()
		}()
		
		if lastErr == nil {
			return nil // 成功
		}
		
		fmt.Printf("第 %d 次尝试失败: %v\n", i+1, lastErr)
		
		if i < maxRetries-1 {
			time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
		}
	}
	
	return fmt.Errorf("重试 %d 次后仍然失败: %w", maxRetries, lastErr)
}

// 10. Web服务器错误处理模拟
type HTTPError struct {
	StatusCode int
	Message    string
	Cause      error
}

func (e HTTPError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("HTTP %d: %s (原因: %v)", e.StatusCode, e.Message, e.Cause)
	}
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

func handleRequest(path string) (response string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = HTTPError{
				StatusCode: 500,
				Message:    "内部服务器错误",
				Cause:      fmt.Errorf("panic: %v", r),
			}
		}
	}()
	
	switch path {
	case "/panic":
		panic("服务器内部错误")
	case "/error":
		return "", HTTPError{
			StatusCode: 400,
			Message:    "请求参数错误",
		}
	case "/notfound":
		return "", HTTPError{
			StatusCode: 404,
			Message:    "页面未找到",
		}
	default:
		return fmt.Sprintf("响应: %s", path), nil
	}
}

func main() {
	fmt.Println("=== Go语言自定义错误和panic/recover示例 ===")
	
	// 1. 自定义错误使用
	fmt.Println("\n--- 自定义错误使用 ---")
	
	numbers := [][]float64{
		{10, 2},
		{15, 0},
		{20, 4},
	}
	
	for _, pair := range numbers {
		result, err := safeDivide(pair[0], pair[1])
		if err != nil {
			fmt.Printf("计算失败: %v\n", err)
			
			// 类型断言检查具体错误类型
			if divErr, ok := err.(DivisionByZeroError); ok {
				fmt.Printf("  被除数: %.2f\n", divErr.Dividend)
			}
		} else {
			fmt.Printf("%.2f ÷ %.2f = %.2f\n", pair[0], pair[1], result)
		}
	}
	
	// 2. 复杂自定义错误
	fmt.Println("\n--- 复杂自定义错误 ---")
	
	userIDs := []string{"user123", "", "404", "timeout", "network"}
	
	for _, userID := range userIDs {
		userData, err := fetchUserData(userID)
		if err != nil {
			fmt.Printf("获取用户 '%s' 失败: %v\n", userID, err)
			
			// 检查错误类型并处理
			switch e := err.(type) {
			case ApplicationError:
				fmt.Printf("  错误代码: %s\n", e.Code)
				if details := e.Details; details != nil {
					fmt.Printf("  详细信息: %v\n", details)
				}
			case NetworkError:
				fmt.Printf("  网络操作: %s %s\n", e.Op, e.URL)
				fmt.Printf("  重试次数: %d\n", e.Retry)
			}
		} else {
			fmt.Printf("获取用户 '%s' 成功: %v\n", userID, userData)
		}
	}
	
	// 3. Panic和Recover基础
	demonstratePanic()
	fmt.Println("程序继续执行...") // 这行会执行，因为panic被recover了
	
	// 4. 安全的数组访问
	fmt.Println("\n--- 安全的数组访问 ---")
	
	arr := []int{1, 2, 3, 4, 5}
	indices := []int{2, 10, -1, 4}
	
	for _, index := range indices {
		value, err := safeArrayAccess(arr, index)
		if err != nil {
			fmt.Printf("访问索引 %d 失败: %v\n", index, err)
		} else {
			fmt.Printf("arr[%d] = %d\n", index, value)
		}
	}
	
	// 5. 资源清理与panic
	resourceCleanupDemo()
	fmt.Println("资源清理演示完成")
	
	// 6. 错误恢复和重试机制
	fmt.Println("\n--- 错误恢复和重试机制 ---")
	
	// 模拟不稳定的操作
	attemptCount := 0
	unstableOperation := func() error {
		attemptCount++
		fmt.Printf("执行操作，尝试次数: %d\n", attemptCount)
		
		if attemptCount < 3 {
			if attemptCount == 1 {
				return fmt.Errorf("网络错误")
			} else {
				panic("系统错误")
			}
		}
		
		return nil // 第3次成功
	}
	
	if err := retryWithRecovery(unstableOperation, 5); err != nil {
		fmt.Printf("最终失败: %v\n", err)
	} else {
		fmt.Println("操作最终成功")
	}
	
	// 7. Web服务器错误处理模拟
	fmt.Println("\n--- Web服务器错误处理模拟 ---")
	
	paths := []string{"/home", "/error", "/notfound", "/panic"}
	
	for _, path := range paths {
		response, err := handleRequest(path)
		if err != nil {
			fmt.Printf("请求 '%s' 失败: %v\n", path, err)
			
			if httpErr, ok := err.(HTTPError); ok {
				fmt.Printf("  HTTP状态码: %d\n", httpErr.StatusCode)
			}
		} else {
			fmt.Printf("请求 '%s' 成功: %s\n", path, response)
		}
	}
	
	// 8. 错误处理策略演示
	fmt.Println("\n--- 错误处理策略 ---")
	
	// 策略1：立即处理
	handleImmediately := func(err error) {
		if err != nil {
			fmt.Printf("立即处理错误: %v\n", err)
			// 执行恢复操作
		}
	}
	
	// 策略2：记录并继续
	logAndContinue := func(err error) {
		if err != nil {
			fmt.Printf("记录错误: %v\n", err)
			// 记录到日志系统
		}
	}
	
	// 策略3：包装并传播
	wrapAndPropagate := func(err error, context string) error {
		if err != nil {
			return fmt.Errorf("%s: %w", context, err)
		}
		return nil
	}
	
	// 演示不同策略
	testErr := fmt.Errorf("测试错误")
	
	handleImmediately(testErr)
	logAndContinue(testErr)
	
	if wrappedErr := wrapAndPropagate(testErr, "业务操作"); wrappedErr != nil {
		fmt.Printf("包装后的错误: %v\n", wrappedErr)
	}
	
	// 9. 最佳实践总结
	fmt.Println("\n--- 最佳实践总结 ---")
	
	fmt.Println("自定义错误最佳实践:")
	fmt.Println("1. 实现error接口的Error()方法")
	fmt.Println("2. 提供有意义的错误信息")
	fmt.Println("3. 包含足够的上下文信息")
	fmt.Println("4. 支持错误包装和展开")
	fmt.Println("5. 使用错误代码进行分类")
	
	fmt.Println("\npanic/recover最佳实践:")
	fmt.Println("1. panic用于程序错误，不是异常处理")
	fmt.Println("2. 在defer中使用recover捕获panic")
	fmt.Println("3. 捕获panic后进行适当的清理")
	fmt.Println("4. 不要忽略recover的返回值")
	fmt.Println("5. 避免在库代码中使用panic")
	fmt.Println("6. 使用panic处理不可恢复的错误")
	fmt.Println("7. 在Web服务器中捕获panic防止崩溃")
}

/*
运行命令: conda activate golang && go run 07_error_handling/02_custom_errors_panic.go

自定义错误和panic/recover总结：

1. 自定义错误类型：
   - 实现error接口
   - 提供更多上下文信息
   - 支持类型断言和错误分类
   - 可以包装其他错误

2. 错误设计原则：
   - 包含足够的上下文
   - 支持错误链和包装
   - 使用错误代码分类
   - 提供用户友好的消息

3. panic特点：
   - 用于程序错误，不是异常处理
   - 会终止当前函数执行
   - 执行defer函数
   - 向上传播直到被recover

4. recover机制：
   - 只能在defer函数中使用
   - 捕获当前协程的panic
   - 返回panic的值
   - 阻止panic继续传播

5. 使用场景：
   - panic：程序错误、不可恢复的错误
   - recover：资源清理、服务器防崩溃
   - 自定义错误：业务逻辑错误

6. 最佳实践：
   - 优先使用error而不是panic
   - 在适当的层级处理错误
   - 使用defer确保资源清理
   - 提供有意义的错误信息
   - 避免在库代码中panic

7. 错误处理策略：
   - 立即处理：在发生处解决
   - 记录并继续：记录错误但继续执行
   - 包装传播：添加上下文后向上传播
   - 重试机制：对临时错误进行重试

8. 注意事项：
   - panic会跨越函数边界
   - recover只能捕获直接调用的panic
   - 不要将panic用作控制流
   - 合理使用错误类型和错误码
*/
