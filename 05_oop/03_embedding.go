// 03_embedding.go - Go语言嵌入(组合)
// 这个文件演示了Go语言中结构体嵌入和接口嵌入的使用

package main

import "fmt"

// 1. 基本结构体嵌入
type Person struct {
	Name string
	Age  int
}

func (p Person) Introduce() string {
	return fmt.Sprintf("我是%s，今年%d岁", p.Name, p.Age)
}

func (p Person) Walk() string {
	return fmt.Sprintf("%s正在走路", p.Name)
}

// 嵌入Person的Student结构体
type Student struct {
	Person    // 匿名字段，嵌入Person
	StudentID string
	Grade     int
}

func (s Student) Study() string {
	return fmt.Sprintf("学生%s正在学习", s.Name) // 可以直接访问Person的字段
}

// 嵌入Person的Teacher结构体
type Teacher struct {
	Person     // 匿名字段，嵌入Person
	EmployeeID string
	Subject    string
}

func (t Teacher) Teach() string {
	return fmt.Sprintf("老师%s正在教%s", t.Name, t.Subject)
}

// 2. 多层嵌入
type Animal struct {
	Name    string
	Species string
}

func (a Animal) Eat() string {
	return fmt.Sprintf("%s正在吃东西", a.Name)
}

func (a Animal) Sleep() string {
	return fmt.Sprintf("%s正在睡觉", a.Name)
}

type Mammal struct {
	Animal      // 嵌入Animal
	FurColor    string
	IsWarmBlood bool
}

func (m Mammal) GiveBirth() string {
	return fmt.Sprintf("%s生了小宝宝", m.Name)
}

type Dog struct {
	Mammal // 嵌入Mammal（间接嵌入Animal）
	Breed  string
}

func (d Dog) Bark() string {
	return fmt.Sprintf("%s在汪汪叫", d.Name)
}

func (d Dog) Fetch() string {
	return fmt.Sprintf("%s去捡球", d.Name)
}

// 3. 方法重写（方法提升和覆盖）
type Vehicle struct {
	Brand string
	Model string
}

func (v Vehicle) Start() string {
	return fmt.Sprintf("%s %s启动了", v.Brand, v.Model)
}

func (v Vehicle) Stop() string {
	return fmt.Sprintf("%s %s停止了", v.Brand, v.Model)
}

type Car struct {
	Vehicle
	Doors int
}

// 重写Start方法
func (c Car) Start() string {
	return fmt.Sprintf("汽车%s %s启动了引擎", c.Brand, c.Model)
}

// Car没有重写Stop方法，会使用Vehicle的Stop方法

type Motorcycle struct {
	Vehicle
	HasSidecar bool
}

// Motorcycle没有重写任何方法，会使用Vehicle的所有方法

// 4. 接口嵌入
type Reader interface {
	Read([]byte) (int, error)
}

type Writer interface {
	Write([]byte) (int, error)
}

type Closer interface {
	Close() error
}

// 嵌入多个接口
type ReadWriteCloser interface {
	Reader
	Writer
	Closer
}

// 实现ReadWriteCloser接口的结构体
type File struct {
	Name    string
	Content []byte
	Pos     int
	Closed  bool
}

func (f *File) Read(p []byte) (int, error) {
	if f.Closed {
		return 0, fmt.Errorf("文件已关闭")
	}
	
	remaining := len(f.Content) - f.Pos
	if remaining == 0 {
		return 0, fmt.Errorf("EOF")
	}
	
	n := copy(p, f.Content[f.Pos:])
	f.Pos += n
	return n, nil
}

func (f *File) Write(p []byte) (int, error) {
	if f.Closed {
		return 0, fmt.Errorf("文件已关闭")
	}
	
	f.Content = append(f.Content, p...)
	return len(p), nil
}

func (f *File) Close() error {
	f.Closed = true
	fmt.Printf("文件%s已关闭\n", f.Name)
	return nil
}

// 5. 嵌入与接口实现
type Flyable interface {
	Fly() string
}

type Swimmable interface {
	Swim() string
}

type Bird struct {
	Name string
}

func (b Bird) Fly() string {
	return fmt.Sprintf("%s在飞翔", b.Name)
}

type Fish struct {
	Name string
}

func (f Fish) Swim() string {
	return fmt.Sprintf("%s在游泳", f.Name)
}

// Duck嵌入Bird和Fish，获得两种能力
type Duck struct {
	Bird
	Fish
}

func (d Duck) Quack() string {
	return fmt.Sprintf("鸭子%s在嘎嘎叫", d.Bird.Name) // 需要指定是哪个嵌入字段的Name
}

// 6. 嵌入指针类型
type Engine struct {
	Power int
	Type  string
}

func (e *Engine) Start() {
	fmt.Printf("%s引擎启动，功率%d马力\n", e.Type, e.Power)
}

func (e *Engine) Stop() {
	fmt.Printf("%s引擎停止\n", e.Type)
}

type SportsCar struct {
	*Engine // 嵌入指针类型
	Brand   string
	TopSpeed int
}

func main() {
	fmt.Println("=== Go语言嵌入(组合)示例 ===")
	
	// 1. 基本结构体嵌入使用
	fmt.Println("\n--- 基本结构体嵌入使用 ---")
	
	student := Student{
		Person:    Person{Name: "小明", Age: 18},
		StudentID: "S001",
		Grade:     12,
	}
	
	// 可以直接访问嵌入结构体的字段和方法
	fmt.Printf("学生姓名: %s\n", student.Name)              // 输出: 学生姓名: 小明
	fmt.Printf("学生年龄: %d\n", student.Age)               // 输出: 学生年龄: 18
	fmt.Printf("学生ID: %s\n", student.StudentID)          // 输出: 学生ID: S001
	
	fmt.Println(student.Introduce())                      // 输出: 我是小明，今年18岁
	fmt.Println(student.Walk())                           // 输出: 小明正在走路
	fmt.Println(student.Study())                          // 输出: 学生小明正在学习
	
	teacher := Teacher{
		Person:     Person{Name: "王老师", Age: 35},
		EmployeeID: "T001",
		Subject:    "数学",
	}
	
	fmt.Println(teacher.Introduce())                      // 输出: 我是王老师，今年35岁
	fmt.Println(teacher.Teach())                          // 输出: 老师王老师正在教数学
	
	// 2. 多层嵌入使用
	fmt.Println("\n--- 多层嵌入使用 ---")
	
	dog := Dog{
		Mammal: Mammal{
			Animal: Animal{
				Name:    "旺财",
				Species: "犬科",
			},
			FurColor:    "棕色",
			IsWarmBlood: true,
		},
		Breed: "金毛",
	}
	
	// 可以访问所有层级的字段和方法
	fmt.Printf("狗的名字: %s\n", dog.Name)                 // 输出: 狗的名字: 旺财 (来自Animal)
	fmt.Printf("狗的品种: %s\n", dog.Breed)                // 输出: 狗的品种: 金毛
	fmt.Printf("毛色: %s\n", dog.FurColor)                 // 输出: 毛色: 棕色 (来自Mammal)
	
	fmt.Println(dog.Eat())                                // 输出: 旺财正在吃东西 (来自Animal)
	fmt.Println(dog.GiveBirth())                          // 输出: 旺财生了小宝宝 (来自Mammal)
	fmt.Println(dog.Bark())                               // 输出: 旺财在汪汪叫 (Dog自己的方法)
	fmt.Println(dog.Fetch())                              // 输出: 旺财去捡球 (Dog自己的方法)
	
	// 3. 方法重写示例
	fmt.Println("\n--- 方法重写示例 ---")
	
	car := Car{
		Vehicle: Vehicle{Brand: "丰田", Model: "卡罗拉"},
		Doors:   4,
	}
	
	motorcycle := Motorcycle{
		Vehicle:    Vehicle{Brand: "哈雷", Model: "戴维森"},
		HasSidecar: false,
	}
	
	fmt.Println(car.Start())                              // 输出: 汽车丰田 卡罗拉启动了引擎 (重写的方法)
	fmt.Println(car.Stop())                               // 输出: 丰田 卡罗拉停止了 (继承的方法)
	
	fmt.Println(motorcycle.Start())                       // 输出: 哈雷 戴维森启动了 (继承的方法)
	fmt.Println(motorcycle.Stop())                        // 输出: 哈雷 戴维森停止了 (继承的方法)
	
	// 4. 接口嵌入使用
	fmt.Println("\n--- 接口嵌入使用 ---")
	
	file := &File{
		Name:    "test.txt",
		Content: []byte("Hello, World!"),
		Pos:     0,
		Closed:  false,
	}
	
	// File实现了ReadWriteCloser接口
	var rwc ReadWriteCloser = file
	
	// 读取文件
	buffer := make([]byte, 5)
	n, err := rwc.Read(buffer)
	if err != nil {
		fmt.Printf("读取错误: %v\n", err)
	} else {
		fmt.Printf("读取了%d字节: %s\n", n, string(buffer[:n])) // 输出: 读取了5字节: Hello
	}
	
	// 写入文件
	n, err = rwc.Write([]byte(" Go!"))
	if err != nil {
		fmt.Printf("写入错误: %v\n", err)
	} else {
		fmt.Printf("写入了%d字节\n", n)                        // 输出: 写入了4字节
	}
	
	// 关闭文件
	rwc.Close()                                           // 输出: 文件test.txt已关闭
	
	// 5. 嵌入与接口实现
	fmt.Println("\n--- 嵌入与接口实现 ---")
	
	duck := Duck{
		Bird: Bird{Name: "唐老鸭"},
		Fish: Fish{Name: "唐老鸭"}, // 注意：两个嵌入字段都有Name
	}
	
	// Duck通过嵌入获得了Fly和Swim能力
	var flyable Flyable = duck
	var swimmable Swimmable = duck
	
	fmt.Println(flyable.Fly())                            // 输出: 唐老鸭在飞翔
	fmt.Println(swimmable.Swim())                         // 输出: 唐老鸭在游泳
	fmt.Println(duck.Quack())                             // 输出: 鸭子唐老鸭在嘎嘎叫
	
	// 6. 嵌入指针类型
	fmt.Println("\n--- 嵌入指针类型 ---")
	
	engine := &Engine{Power: 500, Type: "V8"}
	sportsCar := SportsCar{
		Engine:   engine,
		Brand:    "法拉利",
		TopSpeed: 320,
	}
	
	fmt.Printf("跑车品牌: %s\n", sportsCar.Brand)           // 输出: 跑车品牌: 法拉利
	fmt.Printf("最高时速: %d km/h\n", sportsCar.TopSpeed)   // 输出: 最高时速: 320 km/h
	fmt.Printf("引擎功率: %d马力\n", sportsCar.Power)        // 输出: 引擎功率: 500马力 (通过嵌入访问)
	
	sportsCar.Start()                                     // 输出: V8引擎启动，功率500马力
	sportsCar.Stop()                                      // 输出: V8引擎停止
	
	// 7. 字段名冲突处理
	fmt.Println("\n--- 字段名冲突处理 ---")
	
	type A struct {
		Name string
		Value int
	}
	
	type B struct {
		Name  string
		Count int
	}
	
	type C struct {
		A
		B
		Name string // C自己的Name字段
	}
	
	c := C{
		A:    A{Name: "A的名字", Value: 1},
		B:    B{Name: "B的名字", Count: 2},
		Name: "C的名字",
	}
	
	fmt.Printf("C.Name: %s\n", c.Name)                    // 输出: C.Name: C的名字 (C自己的字段)
	fmt.Printf("C.A.Name: %s\n", c.A.Name)                // 输出: C.A.Name: A的名字 (A的字段)
	fmt.Printf("C.B.Name: %s\n", c.B.Name)                // 输出: C.B.Name: B的名字 (B的字段)
	
	// 8. 实际应用：HTTP处理器
	fmt.Println("\n--- 实际应用：HTTP处理器 ---")
	
	type BaseHandler struct {
		Name string
	}
	
	func (bh BaseHandler) Log(message string) {
		fmt.Printf("[%s] %s\n", bh.Name, message)
	}
	
	type UserHandler struct {
		BaseHandler
	}
	
	func (uh UserHandler) GetUser(id int) {
		uh.Log(fmt.Sprintf("获取用户ID: %d", id))
		// 实际的用户获取逻辑
	}
	
	func (uh UserHandler) CreateUser(name string) {
		uh.Log(fmt.Sprintf("创建用户: %s", name))
		// 实际的用户创建逻辑
	}
	
	type ProductHandler struct {
		BaseHandler
	}
	
	func (ph ProductHandler) GetProduct(id int) {
		ph.Log(fmt.Sprintf("获取产品ID: %d", id))
		// 实际的产品获取逻辑
	}
	
	// 使用处理器
	userHandler := UserHandler{
		BaseHandler: BaseHandler{Name: "UserHandler"},
	}
	
	productHandler := ProductHandler{
		BaseHandler: BaseHandler{Name: "ProductHandler"},
	}
	
	userHandler.GetUser(123)                              // 输出: [UserHandler] 获取用户ID: 123
	userHandler.CreateUser("张三")                         // 输出: [UserHandler] 创建用户: 张三
	productHandler.GetProduct(456)                        // 输出: [ProductHandler] 获取产品ID: 456
	
	// 9. 嵌入的最佳实践
	fmt.Println("\n--- 嵌入的最佳实践 ---")
	
	fmt.Println("Go语言嵌入最佳实践:")
	fmt.Println("1. 优先使用组合而不是继承")
	fmt.Println("2. 嵌入应该表示'has-a'关系，而不是'is-a'关系")
	fmt.Println("3. 避免过深的嵌入层次")
	fmt.Println("4. 注意字段名冲突，必要时使用显式字段名")
	fmt.Println("5. 嵌入指针类型时要注意nil检查")
	fmt.Println("6. 接口嵌入用于组合小接口成大接口")
	fmt.Println("7. 方法提升让代码更简洁，但要注意方法覆盖")
}

/*
运行命令: conda activate golang && go run 05_oop/03_embedding.go

Go语言嵌入(组合)总结：

1. 结构体嵌入：
   - 匿名字段：type Outer struct { Inner }
   - 字段提升：可以直接访问嵌入类型的字段
   - 方法提升：可以直接调用嵌入类型的方法

2. 嵌入特点：
   - 实现组合而非继承
   - 支持多层嵌入
   - 支持多个嵌入
   - 可以嵌入指针类型

3. 方法覆盖：
   - 外层类型可以重写嵌入类型的方法
   - 未重写的方法会自动提升
   - 通过类型名可以访问被覆盖的方法

4. 接口嵌入：
   - 接口可以嵌入其他接口
   - 实现组合接口需要实现所有方法
   - 用于构建大接口

5. 字段冲突：
   - 同名字段需要显式指定
   - 外层字段会遮蔽内层字段
   - 使用类型名消除歧义

6. 使用场景：
   - 代码复用
   - 功能组合
   - 接口组合
   - 基础功能扩展

7. 最佳实践：
   - 表示has-a关系
   - 避免过深嵌入
   - 注意字段冲突
   - 合理使用方法覆盖
   - 接口保持小而专注
*/
