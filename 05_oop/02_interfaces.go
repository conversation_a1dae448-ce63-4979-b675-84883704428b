// 02_interfaces.go - Go语言接口
// 这个文件演示了Go语言中接口的定义、实现和使用

package main

import (
	"fmt"
	"math"
)

// 1. 基本接口定义
type Shape interface {
	Area() float64
	Perimeter() float64
}

// 2. 实现接口的结构体
type Rectangle struct {
	Width, Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

type Circle struct {
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

// 3. 更复杂的接口
type Animal interface {
	Speak() string
	Move() string
}

type Dog struct {
	Name string
}

func (d Dog) Speak() string {
	return "汪汪"
}

func (d Dog) Move() string {
	return "跑步"
}

type Cat struct {
	Name string
}

func (c Cat) Speak() string {
	return "喵喵"
}

func (c <PERSON>) Move() string {
	return "悄悄走"
}

type Bird struct {
	Name string
}

func (b <PERSON>) Speak() string {
	return "啾啾"
}

func (b Bird) Move() string {
	return "飞翔"
}

// 4. 接口嵌套
type Reader interface {
	Read([]byte) (int, error)
}

type Writer interface {
	Write([]byte) (int, error)
}

type ReadWriter interface {
	Reader
	Writer
}

// 5. 空接口
func processAnyType(value interface{}) {
	fmt.Printf("值: %v, 类型: %T\n", value, value)
}

// 6. 类型断言和类型开关
func describeType(i interface{}) {
	switch v := i.(type) {
	case int:
		fmt.Printf("整数: %d\n", v)
	case string:
		fmt.Printf("字符串: %s\n", v)
	case bool:
		fmt.Printf("布尔值: %t\n", v)
	case Shape:
		fmt.Printf("图形，面积: %.2f\n", v.Area())
	default:
		fmt.Printf("未知类型: %T\n", v)
	}
}

// 7. 实际应用：数据库接口
type Database interface {
	Connect() error
	Query(sql string) ([]map[string]interface{}, error)
	Close() error
}

type MySQL struct {
	Host     string
	Port     int
	Username string
	Password string
}

func (m *MySQL) Connect() error {
	fmt.Printf("连接到MySQL: %s:%d\n", m.Host, m.Port)
	return nil
}

func (m *MySQL) Query(sql string) ([]map[string]interface{}, error) {
	fmt.Printf("执行MySQL查询: %s\n", sql)
	return []map[string]interface{}{
		{"id": 1, "name": "张三"},
		{"id": 2, "name": "李四"},
	}, nil
}

func (m *MySQL) Close() error {
	fmt.Println("关闭MySQL连接")
	return nil
}

type PostgreSQL struct {
	ConnectionString string
}

func (p *PostgreSQL) Connect() error {
	fmt.Printf("连接到PostgreSQL: %s\n", p.ConnectionString)
	return nil
}

func (p *PostgreSQL) Query(sql string) ([]map[string]interface{}, error) {
	fmt.Printf("执行PostgreSQL查询: %s\n", sql)
	return []map[string]interface{}{
		{"id": 1, "name": "王五"},
		{"id": 2, "name": "赵六"},
	}, nil
}

func (p *PostgreSQL) Close() error {
	fmt.Println("关闭PostgreSQL连接")
	return nil
}

// 8. 实际应用：支付接口
type PaymentProcessor interface {
	ProcessPayment(amount float64) error
	GetTransactionID() string
}

type CreditCard struct {
	CardNumber     string
	TransactionID  string
}

func (cc *CreditCard) ProcessPayment(amount float64) error {
	cc.TransactionID = fmt.Sprintf("CC-%d", int(amount*100))
	fmt.Printf("信用卡支付: %.2f元, 交易ID: %s\n", amount, cc.TransactionID)
	return nil
}

func (cc *CreditCard) GetTransactionID() string {
	return cc.TransactionID
}

type PayPal struct {
	Email         string
	TransactionID string
}

func (pp *PayPal) ProcessPayment(amount float64) error {
	pp.TransactionID = fmt.Sprintf("PP-%d", int(amount*100))
	fmt.Printf("PayPal支付: %.2f元, 账户: %s, 交易ID: %s\n", amount, pp.Email, pp.TransactionID)
	return nil
}

func (pp *PayPal) GetTransactionID() string {
	return pp.TransactionID
}

func main() {
	fmt.Println("=== Go语言接口示例 ===")
	
	// 1. 基本接口使用
	fmt.Println("\n--- 基本接口使用 ---")
	
	var shapes []Shape
	shapes = append(shapes, Rectangle{Width: 5, Height: 3})
	shapes = append(shapes, Circle{Radius: 4})
	
	for i, shape := range shapes {
		fmt.Printf("图形%d: 面积=%.2f, 周长=%.2f\n", 
			i+1, shape.Area(), shape.Perimeter())
	}
	// 输出:
	// 图形1: 面积=15.00, 周长=16.00
	// 图形2: 面积=50.27, 周长=25.13
	
	// 2. 接口变量赋值
	fmt.Println("\n--- 接口变量赋值 ---")
	
	var s Shape
	s = Rectangle{Width: 4, Height: 6}
	fmt.Printf("矩形面积: %.2f\n", s.Area()) // 输出: 矩形面积: 24.00
	
	s = Circle{Radius: 3}
	fmt.Printf("圆形面积: %.2f\n", s.Area()) // 输出: 圆形面积: 28.27
	
	// 3. 动物接口使用
	fmt.Println("\n--- 动物接口使用 ---")
	
	animals := []Animal{
		Dog{Name: "旺财"},
		Cat{Name: "咪咪"},
		Bird{Name: "小鸟"},
	}
	
	for _, animal := range animals {
		fmt.Printf("动物叫声: %s, 移动方式: %s\n", animal.Speak(), animal.Move())
	}
	// 输出:
	// 动物叫声: 汪汪, 移动方式: 跑步
	// 动物叫声: 喵喵, 移动方式: 悄悄走
	// 动物叫声: 啾啾, 移动方式: 飞翔
	
	// 4. 空接口使用
	fmt.Println("\n--- 空接口使用 ---")
	
	processAnyType(42)                    // 输出: 值: 42, 类型: int
	processAnyType("Hello")               // 输出: 值: Hello, 类型: string
	processAnyType(true)                  // 输出: 值: true, 类型: bool
	processAnyType([]int{1, 2, 3})       // 输出: 值: [1 2 3], 类型: []int
	
	// 5. 类型断言
	fmt.Println("\n--- 类型断言 ---")
	
	var i interface{} = "Hello, World!"
	
	// 安全的类型断言
	if str, ok := i.(string); ok {
		fmt.Printf("字符串值: %s\n", str) // 输出: 字符串值: Hello, World!
	}
	
	// 不安全的类型断言（如果类型不匹配会panic）
	str := i.(string)
	fmt.Printf("断言的字符串: %s\n", str) // 输出: 断言的字符串: Hello, World!
	
	// 检查是否实现了接口
	var shape Shape = Circle{Radius: 5}
	if circle, ok := shape.(Circle); ok {
		fmt.Printf("这是一个圆，半径: %.2f\n", circle.Radius) // 输出: 这是一个圆，半径: 5.00
	}
	
	// 6. 类型开关
	fmt.Println("\n--- 类型开关 ---")
	
	values := []interface{}{
		42,
		"Hello",
		true,
		Rectangle{Width: 3, Height: 4},
		Circle{Radius: 2},
		[]int{1, 2, 3},
	}
	
	for _, value := range values {
		describeType(value)
	}
	// 输出:
	// 整数: 42
	// 字符串: Hello
	// 布尔值: true
	// 图形，面积: 12.00
	// 图形，面积: 12.57
	// 未知类型: []int
	
	// 7. 数据库接口使用
	fmt.Println("\n--- 数据库接口使用 ---")
	
	// 使用不同的数据库实现
	databases := []Database{
		&MySQL{Host: "localhost", Port: 3306, Username: "root", Password: "password"},
		&PostgreSQL{ConnectionString: "postgres://user:pass@localhost/db"},
	}
	
	for i, db := range databases {
		fmt.Printf("\n数据库%d:\n", i+1)
		db.Connect()
		results, _ := db.Query("SELECT * FROM users")
		fmt.Printf("查询结果: %v\n", results)
		db.Close()
	}
	
	// 8. 支付接口使用
	fmt.Println("\n--- 支付接口使用 ---")
	
	processors := []PaymentProcessor{
		&CreditCard{CardNumber: "1234-5678-9012-3456"},
		&PayPal{Email: "<EMAIL>"},
	}
	
	amount := 99.99
	for _, processor := range processors {
		processor.ProcessPayment(amount)
		fmt.Printf("交易ID: %s\n\n", processor.GetTransactionID())
	}
	
	// 9. 接口的零值
	fmt.Println("\n--- 接口的零值 ---")
	
	var nilShape Shape
	fmt.Printf("nil接口: %v\n", nilShape)           // 输出: nil接口: <nil>
	fmt.Printf("nil接口是否为nil: %t\n", nilShape == nil) // 输出: nil接口是否为nil: true
	
	// 注意：接口包含类型和值两部分
	var nilPtr *Rectangle
	var shapeWithNilPtr Shape = nilPtr
	fmt.Printf("包含nil指针的接口是否为nil: %t\n", shapeWithNilPtr == nil) // 输出: false
	
	// 10. 接口的动态类型和动态值
	fmt.Println("\n--- 接口的动态类型和动态值 ---")
	
	var animal Animal
	
	// 赋值Dog
	animal = Dog{Name: "旺财"}
	fmt.Printf("动态类型: %T, 动态值: %v\n", animal, animal)
	
	// 赋值Cat
	animal = Cat{Name: "咪咪"}
	fmt.Printf("动态类型: %T, 动态值: %v\n", animal, animal)
	
	// 11. 接口比较
	fmt.Println("\n--- 接口比较 ---")
	
	var a1, a2 Animal
	a1 = Dog{Name: "旺财"}
	a2 = Dog{Name: "旺财"}
	
	fmt.Printf("相同类型相同值的接口相等: %t\n", a1 == a2) // 输出: true
	
	a2 = Cat{Name: "咪咪"}
	fmt.Printf("不同类型的接口相等: %t\n", a1 == a2)     // 输出: false
	
	// 12. 实际应用：策略模式
	fmt.Println("\n--- 实际应用：策略模式 ---")
	
	type SortStrategy interface {
		Sort([]int) []int
	}
	
	type BubbleSort struct{}
	
	func (bs BubbleSort) Sort(data []int) []int {
		fmt.Println("使用冒泡排序")
		result := make([]int, len(data))
		copy(result, data)
		// 简化的冒泡排序实现
		for i := 0; i < len(result); i++ {
			for j := 0; j < len(result)-1-i; j++ {
				if result[j] > result[j+1] {
					result[j], result[j+1] = result[j+1], result[j]
				}
			}
		}
		return result
	}
	
	type QuickSort struct{}
	
	func (qs QuickSort) Sort(data []int) []int {
		fmt.Println("使用快速排序")
		result := make([]int, len(data))
		copy(result, data)
		// 这里只是示例，实际应该实现快速排序算法
		// 为了简单，我们使用Go的内置排序
		for i := 0; i < len(result); i++ {
			for j := i + 1; j < len(result); j++ {
				if result[i] > result[j] {
					result[i], result[j] = result[j], result[i]
				}
			}
		}
		return result
	}
	
	type Sorter struct {
		strategy SortStrategy
	}
	
	func (s *Sorter) SetStrategy(strategy SortStrategy) {
		s.strategy = strategy
	}
	
	func (s *Sorter) Sort(data []int) []int {
		return s.strategy.Sort(data)
	}
	
	// 使用策略模式
	data := []int{64, 34, 25, 12, 22, 11, 90}
	sorter := &Sorter{}
	
	fmt.Printf("原始数据: %v\n", data)
	
	// 使用冒泡排序
	sorter.SetStrategy(BubbleSort{})
	result1 := sorter.Sort(data)
	fmt.Printf("冒泡排序结果: %v\n", result1)
	
	// 使用快速排序
	sorter.SetStrategy(QuickSort{})
	result2 := sorter.Sort(data)
	fmt.Printf("快速排序结果: %v\n", result2)
	
	// 13. 接口最佳实践
	fmt.Println("\n--- 接口最佳实践 ---")
	
	fmt.Println("Go语言接口最佳实践:")
	fmt.Println("1. 接口应该小而专注（单一职责）")
	fmt.Println("2. 接受接口，返回具体类型")
	fmt.Println("3. 接口定义在使用方，而不是实现方")
	fmt.Println("4. 使用组合而不是继承")
	fmt.Println("5. 空接口interface{}应该谨慎使用")
	fmt.Println("6. 接口名通常以-er结尾")
	fmt.Println("7. 优先使用小接口组合成大接口")
}

/*
运行命令: conda activate golang && go run 05_oop/02_interfaces.go

Go语言接口总结：

1. 接口特点：
   - 定义方法签名的集合
   - 隐式实现（duck typing）
   - 接口是类型，可以作为变量类型
   - 零值是nil

2. 接口定义：
   type InterfaceName interface {
       Method1() returnType
       Method2(param type) returnType
   }

3. 接口实现：
   - 无需显式声明实现接口
   - 只要实现了接口的所有方法就自动实现了接口
   - 一个类型可以实现多个接口

4. 空接口：
   - interface{}可以存储任何类型的值
   - 类似其他语言的Object或Any
   - 需要类型断言来获取具体值

5. 类型断言：
   - value, ok := interface.(Type)：安全断言
   - value := interface.(Type)：不安全断言
   - 用于从接口中获取具体类型

6. 类型开关：
   - switch v := i.(type)
   - 根据接口的动态类型执行不同逻辑

7. 接口组合：
   - 接口可以嵌套其他接口
   - 实现组合接口需要实现所有方法

8. 常见应用：
   - 多态性
   - 依赖注入
   - 策略模式
   - 适配器模式
   - 模拟测试

9. 最佳实践：
   - 接口应该小而专注
   - 在使用方定义接口
   - 接受接口，返回具体类型
   - 合理使用空接口
*/
