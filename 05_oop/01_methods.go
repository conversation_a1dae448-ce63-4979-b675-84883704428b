// 01_methods.go - Go语言方法
// 这个文件演示了Go语言中方法的定义和使用

package main

import (
	"fmt"
	"math"
)

// 1. 为结构体定义方法
type Rectangle struct {
	Width  float64
	Height float64
}

// 值接收者方法
func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

// 值接收者方法
func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

// 指针接收者方法
func (r *Rectangle) Scale(factor float64) {
	r.Width *= factor
	r.Height *= factor
}

// 指针接收者方法
func (r *Rectangle) SetDimensions(width, height float64) {
	r.Width = width
	r.Height = height
}

// 2. 为基本类型定义方法（通过类型别名）
type MyInt int

func (m MyInt) Double() MyInt {
	return m * 2
}

func (m MyInt) IsEven() bool {
	return m%2 == 0
}

func (m *MyInt) Increment() {
	*m++
}

// 3. 为切片类型定义方法
type IntSlice []int

func (s IntSlice) Sum() int {
	total := 0
	for _, v := range s {
		total += v
	}
	return total
}

func (s IntSlice) Average() float64 {
	if len(s) == 0 {
		return 0
	}
	return float64(s.Sum()) / float64(len(s))
}

func (s *IntSlice) Append(value int) {
	*s = append(*s, value)
}

// 4. 复杂结构体的方法
type Person struct {
	FirstName string
	LastName  string
	Age       int
	Email     string
}

func (p Person) FullName() string {
	return p.FirstName + " " + p.LastName
}

func (p Person) IsAdult() bool {
	return p.Age >= 18
}

func (p Person) Introduce() string {
	return fmt.Sprintf("我叫%s，今年%d岁", p.FullName(), p.Age)
}

func (p *Person) SetEmail(email string) {
	p.Email = email
}

func (p *Person) HaveBirthday() {
	p.Age++
}

// 5. 银行账户示例
type BankAccount struct {
	AccountNumber string
	HolderName    string
	Balance       float64
}

func (b BankAccount) GetBalance() float64 {
	return b.Balance
}

func (b BankAccount) GetAccountInfo() string {
	return fmt.Sprintf("账户: %s, 持有人: %s, 余额: %.2f", 
		b.AccountNumber, b.HolderName, b.Balance)
}

func (b *BankAccount) Deposit(amount float64) error {
	if amount <= 0 {
		return fmt.Errorf("存款金额必须大于0")
	}
	b.Balance += amount
	return nil
}

func (b *BankAccount) Withdraw(amount float64) error {
	if amount <= 0 {
		return fmt.Errorf("取款金额必须大于0")
	}
	if amount > b.Balance {
		return fmt.Errorf("余额不足")
	}
	b.Balance -= amount
	return nil
}

func (b *BankAccount) Transfer(to *BankAccount, amount float64) error {
	if err := b.Withdraw(amount); err != nil {
		return err
	}
	if err := to.Deposit(amount); err != nil {
		// 如果存款失败，回滚取款
		b.Deposit(amount)
		return err
	}
	return nil
}

func main() {
	fmt.Println("=== Go语言方法示例 ===")
	
	// 1. 结构体方法使用
	fmt.Println("\n--- 结构体方法使用 ---")
	
	rect := Rectangle{Width: 5, Height: 3}
	fmt.Printf("矩形: %+v\n", rect)                    // 输出: 矩形: {Width:5 Height:3}
	fmt.Printf("面积: %.2f\n", rect.Area())            // 输出: 面积: 15.00
	fmt.Printf("周长: %.2f\n", rect.Perimeter())       // 输出: 周长: 16.00
	
	// 调用指针接收者方法
	fmt.Printf("缩放前: %+v\n", rect)
	rect.Scale(2) // Go会自动取地址：(&rect).Scale(2)
	fmt.Printf("缩放2倍后: %+v\n", rect)               // 输出: 缩放2倍后: {Width:10 Height:6}
	
	// 使用指针调用方法
	rectPtr := &Rectangle{Width: 4, Height: 2}
	fmt.Printf("指针矩形面积: %.2f\n", rectPtr.Area()) // Go会自动解引用：(*rectPtr).Area()
	rectPtr.SetDimensions(8, 4)
	fmt.Printf("设置尺寸后: %+v\n", *rectPtr)          // 输出: 设置尺寸后: {Width:8 Height:4}
	
	// 2. 基本类型方法使用
	fmt.Println("\n--- 基本类型方法使用 ---")
	
	var num MyInt = 5
	fmt.Printf("数字: %d\n", num)                      // 输出: 数字: 5
	fmt.Printf("双倍: %d\n", num.Double())             // 输出: 双倍: 10
	fmt.Printf("是否偶数: %t\n", num.IsEven())          // 输出: 是否偶数: false
	
	fmt.Printf("自增前: %d\n", num)
	num.Increment()
	fmt.Printf("自增后: %d\n", num)                    // 输出: 自增后: 6
	
	// 3. 切片类型方法使用
	fmt.Println("\n--- 切片类型方法使用 ---")
	
	numbers := IntSlice{1, 2, 3, 4, 5}
	fmt.Printf("数字切片: %v\n", numbers)              // 输出: 数字切片: [1 2 3 4 5]
	fmt.Printf("总和: %d\n", numbers.Sum())           // 输出: 总和: 15
	fmt.Printf("平均值: %.2f\n", numbers.Average())    // 输出: 平均值: 3.00
	
	numbers.Append(6)
	fmt.Printf("添加元素后: %v\n", numbers)            // 输出: 添加元素后: [1 2 3 4 5 6]
	
	// 4. 复杂结构体方法使用
	fmt.Println("\n--- 复杂结构体方法使用 ---")
	
	person := Person{
		FirstName: "张",
		LastName:  "三",
		Age:       25,
		Email:     "<EMAIL>",
	}
	
	fmt.Printf("全名: %s\n", person.FullName())        // 输出: 全名: 张 三
	fmt.Printf("是否成年: %t\n", person.IsAdult())     // 输出: 是否成年: true
	fmt.Printf("自我介绍: %s\n", person.Introduce())   // 输出: 自我介绍: 我叫张 三，今年25岁
	
	// 修改邮箱
	person.SetEmail("<EMAIL>")
	fmt.Printf("新邮箱: %s\n", person.Email)           // 输出: 新邮箱: <EMAIL>
	
	// 过生日
	fmt.Printf("生日前年龄: %d\n", person.Age)
	person.HaveBirthday()
	fmt.Printf("生日后年龄: %d\n", person.Age)         // 输出: 生日后年龄: 26
	
	// 5. 银行账户方法使用
	fmt.Println("\n--- 银行账户方法使用 ---")
	
	account1 := BankAccount{
		AccountNumber: "ACC001",
		HolderName:    "张三",
		Balance:       1000.0,
	}
	
	account2 := BankAccount{
		AccountNumber: "ACC002",
		HolderName:    "李四",
		Balance:       500.0,
	}
	
	fmt.Println(account1.GetAccountInfo()) // 输出: 账户: ACC001, 持有人: 张三, 余额: 1000.00
	fmt.Println(account2.GetAccountInfo()) // 输出: 账户: ACC002, 持有人: 李四, 余额: 500.00
	
	// 存款
	if err := account1.Deposit(200); err != nil {
		fmt.Printf("存款失败: %v\n", err)
	} else {
		fmt.Printf("存款成功，余额: %.2f\n", account1.GetBalance()) // 输出: 存款成功，余额: 1200.00
	}
	
	// 取款
	if err := account1.Withdraw(300); err != nil {
		fmt.Printf("取款失败: %v\n", err)
	} else {
		fmt.Printf("取款成功，余额: %.2f\n", account1.GetBalance()) // 输出: 取款成功，余额: 900.00
	}
	
	// 转账
	fmt.Printf("转账前 - %s\n", account1.GetAccountInfo())
	fmt.Printf("转账前 - %s\n", account2.GetAccountInfo())
	
	if err := account1.Transfer(&account2, 100); err != nil {
		fmt.Printf("转账失败: %v\n", err)
	} else {
		fmt.Println("转账成功")
		fmt.Printf("转账后 - %s\n", account1.GetAccountInfo()) // 输出: 账户: ACC001, 持有人: 张三, 余额: 800.00
		fmt.Printf("转账后 - %s\n", account2.GetAccountInfo()) // 输出: 账户: ACC002, 持有人: 李四, 余额: 600.00
	}
	
	// 6. 方法值和方法表达式
	fmt.Println("\n--- 方法值和方法表达式 ---")
	
	// 方法值：绑定到特定实例的方法
	r := Rectangle{Width: 3, Height: 4}
	areaMethod := r.Area // 方法值
	fmt.Printf("方法值调用面积: %.2f\n", areaMethod()) // 输出: 方法值调用面积: 12.00
	
	// 方法表达式：未绑定到实例的方法
	areaFunc := Rectangle.Area // 方法表达式
	fmt.Printf("方法表达式调用面积: %.2f\n", areaFunc(r)) // 输出: 方法表达式调用面积: 12.00
	
	// 指针接收者的方法表达式
	scaleFunc := (*Rectangle).Scale
	scaleFunc(&r, 1.5)
	fmt.Printf("缩放后: %+v\n", r) // 输出: 缩放后: {Width:4.5 Height:6}
	
	// 7. 链式方法调用
	fmt.Println("\n--- 链式方法调用 ---")
	
	type Calculator struct {
		value float64
	}
	
	func (c *Calculator) Add(n float64) *Calculator {
		c.value += n
		return c
	}
	
	func (c *Calculator) Multiply(n float64) *Calculator {
		c.value *= n
		return c
	}
	
	func (c *Calculator) Subtract(n float64) *Calculator {
		c.value -= n
		return c
	}
	
	func (c *Calculator) Result() float64 {
		return c.value
	}
	
	calc := &Calculator{value: 10}
	result := calc.Add(5).Multiply(2).Subtract(3).Result()
	fmt.Printf("链式计算结果: %.2f\n", result) // 输出: 链式计算结果: 27.00
	
	// 8. 几何图形示例
	fmt.Println("\n--- 几何图形示例 ---")
	
	type Circle struct {
		Radius float64
	}
	
	func (c Circle) Area() float64 {
		return math.Pi * c.Radius * c.Radius
	}
	
	func (c Circle) Circumference() float64 {
		return 2 * math.Pi * c.Radius
	}
	
	func (c *Circle) SetRadius(radius float64) {
		if radius > 0 {
			c.Radius = radius
		}
	}
	
	circle := Circle{Radius: 5}
	fmt.Printf("圆形半径: %.2f\n", circle.Radius)
	fmt.Printf("圆形面积: %.2f\n", circle.Area())           // 输出: 圆形面积: 78.54
	fmt.Printf("圆形周长: %.2f\n", circle.Circumference())  // 输出: 圆形周长: 31.42
	
	circle.SetRadius(3)
	fmt.Printf("设置半径后面积: %.2f\n", circle.Area())      // 输出: 设置半径后面积: 28.27
	
	// 9. 方法的接收者选择原则
	fmt.Println("\n--- 方法的接收者选择原则 ---")
	
	fmt.Println("值接收者 vs 指针接收者选择原则:")
	fmt.Println("使用指针接收者的情况:")
	fmt.Println("1. 方法需要修改接收者")
	fmt.Println("2. 接收者是大型结构体，避免复制")
	fmt.Println("3. 保持一致性（如果有指针接收者方法，其他方法也用指针）")
	fmt.Println()
	fmt.Println("使用值接收者的情况:")
	fmt.Println("1. 方法不需要修改接收者")
	fmt.Println("2. 接收者是小型结构体或基本类型")
	fmt.Println("3. 接收者是不可变的")
	
	// 10. 方法集合
	fmt.Println("\n--- 方法集合 ---")
	
	type TestStruct struct {
		Value int
	}
	
	func (t TestStruct) ValueMethod() {
		fmt.Println("值接收者方法")
	}
	
	func (t *TestStruct) PointerMethod() {
		fmt.Println("指针接收者方法")
	}
	
	// 值类型可以调用值接收者和指针接收者方法
	ts := TestStruct{Value: 1}
	ts.ValueMethod()   // 直接调用
	ts.PointerMethod() // Go自动取地址：(&ts).PointerMethod()
	
	// 指针类型可以调用值接收者和指针接收者方法
	tsp := &TestStruct{Value: 2}
	tsp.ValueMethod()   // Go自动解引用：(*tsp).ValueMethod()
	tsp.PointerMethod() // 直接调用
	
	fmt.Println("方法集合规则:")
	fmt.Println("- 值类型T的方法集合：所有接收者为T的方法")
	fmt.Println("- 指针类型*T的方法集合：所有接收者为T和*T的方法")
	fmt.Println("- Go编译器会自动进行必要的取地址和解引用操作")
}

/*
运行命令: conda activate golang && go run 05_oop/01_methods.go

Go语言方法总结：

1. 方法定义：
   - func (receiver Type) MethodName(params) returnType
   - 接收者可以是值类型或指针类型
   - 方法名首字母大写表示公开方法

2. 接收者类型：
   - 值接收者：func (t Type) Method()
   - 指针接收者：func (t *Type) Method()

3. 接收者选择原则：
   - 需要修改接收者：使用指针接收者
   - 大型结构体：使用指针接收者避免复制
   - 小型不可变数据：使用值接收者
   - 保持一致性：同一类型的方法使用相同接收者类型

4. 方法调用：
   - Go会自动进行取地址和解引用操作
   - 值类型可以调用指针接收者方法
   - 指针类型可以调用值接收者方法

5. 方法值和方法表达式：
   - 方法值：instance.Method
   - 方法表达式：Type.Method

6. 特殊用法：
   - 可以为任何类型定义方法（除了内置类型）
   - 链式方法调用
   - 方法可以返回接收者指针实现链式调用

7. 方法集合：
   - 类型T：接收者为T的方法
   - 类型*T：接收者为T和*T的方法
   - 影响接口实现

8. 最佳实践：
   - 合理选择接收者类型
   - 保持方法简洁
   - 使用有意义的方法名
   - 考虑接口设计
*/
