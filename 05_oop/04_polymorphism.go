// 04_polymorphism.go - Go语言多态
// 这个文件演示了Go语言中多态的实现和使用

package main

import (
	"fmt"
	"math"
)

// 1. 基本多态：图形接口
type Shape interface {
	Area() float64
	Perimeter() float64
	String() string
}

type Rectangle struct {
	Width, Height float64
}

func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

func (r Rectangle) String() string {
	return fmt.Sprintf("矩形(宽:%.2f, 高:%.2f)", r.Width, r.Height)
}

type Circle struct {
	Radius float64
}

func (c Circle) Area() float64 {
	return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
	return 2 * math.Pi * c.Radius
}

func (c Circle) String() string {
	return fmt.Sprintf("圆形(半径:%.2f)", c<PERSON>)
}

type Triangle struct {
	A, B, <PERSON> float64 // 三边长
}

func (t Triangle) Area() float64 {
	// 使用海伦公式计算面积
	s := (t.A + t.B + t.C) / 2
	return math.Sqrt(s * (s - t.A) * (s - t.B) * (s - t.C))
}

func (t Triangle) Perimeter() float64 {
	return t.A + t.B + t.C
}

func (t Triangle) String() string {
	return fmt.Sprintf("三角形(边长:%.2f,%.2f,%.2f)", t.A, t.B, t.C)
}

// 2. 动物多态
type Animal interface {
	Speak() string
	Move() string
	Eat(food string) string
}

type Dog struct {
	Name  string
	Breed string
}

func (d Dog) Speak() string {
	return fmt.Sprintf("%s汪汪叫", d.Name)
}

func (d Dog) Move() string {
	return fmt.Sprintf("%s在跑步", d.Name)
}

func (d Dog) Eat(food string) string {
	return fmt.Sprintf("%s在吃%s", d.Name, food)
}

type Cat struct {
	Name  string
	Color string
}

func (c Cat) Speak() string {
	return fmt.Sprintf("%s喵喵叫", c.Name)
}

func (c Cat) Move() string {
	return fmt.Sprintf("%s在悄悄走", c.Name)
}

func (c Cat) Eat(food string) string {
	return fmt.Sprintf("%s在优雅地吃%s", c.Name, food)
}

type Bird struct {
	Name    string
	Species string
}

func (b Bird) Speak() string {
	return fmt.Sprintf("%s啾啾叫", b.Name)
}

func (b Bird) Move() string {
	return fmt.Sprintf("%s在飞翔", b.Name)
}

func (b Bird) Eat(food string) string {
	return fmt.Sprintf("%s在啄食%s", b.Name, food)
}

// 3. 支付处理多态
type PaymentMethod interface {
	ProcessPayment(amount float64) (string, error)
	GetFee(amount float64) float64
	GetName() string
}

type CreditCard struct {
	CardNumber string
	CardType   string
}

func (cc CreditCard) ProcessPayment(amount float64) (string, error) {
	if amount <= 0 {
		return "", fmt.Errorf("金额必须大于0")
	}
	transactionID := fmt.Sprintf("CC-%s-%.0f", cc.CardType, amount*100)
	return transactionID, nil
}

func (cc CreditCard) GetFee(amount float64) float64 {
	return amount * 0.025 // 2.5%手续费
}

func (cc CreditCard) GetName() string {
	return fmt.Sprintf("%s信用卡", cc.CardType)
}

type PayPal struct {
	Email string
}

func (pp PayPal) ProcessPayment(amount float64) (string, error) {
	if amount <= 0 {
		return "", fmt.Errorf("金额必须大于0")
	}
	transactionID := fmt.Sprintf("PP-%s-%.0f", pp.Email, amount*100)
	return transactionID, nil
}

func (pp PayPal) GetFee(amount float64) float64 {
	return amount * 0.035 // 3.5%手续费
}

func (pp PayPal) GetName() string {
	return "PayPal"
}

type BankTransfer struct {
	BankName      string
	AccountNumber string
}

func (bt BankTransfer) ProcessPayment(amount float64) (string, error) {
	if amount <= 0 {
		return "", fmt.Errorf("金额必须大于0")
	}
	transactionID := fmt.Sprintf("BT-%s-%.0f", bt.BankName, amount*100)
	return transactionID, nil
}

func (bt BankTransfer) GetFee(amount float64) float64 {
	return 5.0 // 固定5元手续费
}

func (bt BankTransfer) GetName() string {
	return fmt.Sprintf("%s银行转账", bt.BankName)
}

// 4. 数据存储多态
type DataStore interface {
	Save(key string, value interface{}) error
	Load(key string) (interface{}, error)
	Delete(key string) error
	List() []string
}

type MemoryStore struct {
	data map[string]interface{}
}

func NewMemoryStore() *MemoryStore {
	return &MemoryStore{
		data: make(map[string]interface{}),
	}
}

func (ms *MemoryStore) Save(key string, value interface{}) error {
	ms.data[key] = value
	return nil
}

func (ms *MemoryStore) Load(key string) (interface{}, error) {
	value, exists := ms.data[key]
	if !exists {
		return nil, fmt.Errorf("键 %s 不存在", key)
	}
	return value, nil
}

func (ms *MemoryStore) Delete(key string) error {
	delete(ms.data, key)
	return nil
}

func (ms *MemoryStore) List() []string {
	keys := make([]string, 0, len(ms.data))
	for key := range ms.data {
		keys = append(keys, key)
	}
	return keys
}

type FileStore struct {
	filename string
	data     map[string]interface{}
}

func NewFileStore(filename string) *FileStore {
	return &FileStore{
		filename: filename,
		data:     make(map[string]interface{}),
	}
}

func (fs *FileStore) Save(key string, value interface{}) error {
	fs.data[key] = value
	fmt.Printf("保存到文件 %s: %s = %v\n", fs.filename, key, value)
	return nil
}

func (fs *FileStore) Load(key string) (interface{}, error) {
	value, exists := fs.data[key]
	if !exists {
		return nil, fmt.Errorf("文件 %s 中键 %s 不存在", fs.filename, key)
	}
	return value, nil
}

func (fs *FileStore) Delete(key string) error {
	delete(fs.data, key)
	fmt.Printf("从文件 %s 删除: %s\n", fs.filename, key)
	return nil
}

func (fs *FileStore) List() []string {
	keys := make([]string, 0, len(fs.data))
	for key := range fs.data {
		keys = append(keys, key)
	}
	return keys
}

// 5. 多态函数示例
func calculateTotalArea(shapes []Shape) float64 {
	total := 0.0
	for _, shape := range shapes {
		total += shape.Area()
	}
	return total
}

func printShapeInfo(shape Shape) {
	fmt.Printf("%s - 面积: %.2f, 周长: %.2f\n", 
		shape.String(), shape.Area(), shape.Perimeter())
}

func feedAnimals(animals []Animal, food string) {
	for _, animal := range animals {
		fmt.Println(animal.Eat(food))
	}
}

func processPayments(methods []PaymentMethod, amount float64) {
	for _, method := range methods {
		fmt.Printf("\n使用 %s 支付 %.2f 元:\n", method.GetName(), amount)
		
		fee := method.GetFee(amount)
		fmt.Printf("手续费: %.2f 元\n", fee)
		
		transactionID, err := method.ProcessPayment(amount)
		if err != nil {
			fmt.Printf("支付失败: %v\n", err)
		} else {
			fmt.Printf("支付成功，交易ID: %s\n", transactionID)
		}
	}
}

func testDataStore(store DataStore, storeName string) {
	fmt.Printf("\n测试 %s:\n", storeName)
	
	// 保存数据
	store.Save("user1", "张三")
	store.Save("user2", "李四")
	store.Save("config", map[string]string{"theme": "dark", "lang": "zh"})
	
	// 加载数据
	if value, err := store.Load("user1"); err != nil {
		fmt.Printf("加载失败: %v\n", err)
	} else {
		fmt.Printf("加载成功: user1 = %v\n", value)
	}
	
	// 列出所有键
	keys := store.List()
	fmt.Printf("所有键: %v\n", keys)
	
	// 删除数据
	store.Delete("user2")
	
	// 再次列出键
	keys = store.List()
	fmt.Printf("删除后的键: %v\n", keys)
}

func main() {
	fmt.Println("=== Go语言多态示例 ===")
	
	// 1. 图形多态
	fmt.Println("\n--- 图形多态 ---")
	
	shapes := []Shape{
		Rectangle{Width: 5, Height: 3},
		Circle{Radius: 4},
		Triangle{A: 3, B: 4, C: 5},
	}
	
	fmt.Println("所有图形信息:")
	for _, shape := range shapes {
		printShapeInfo(shape)
	}
	// 输出:
	// 矩形(宽:5.00, 高:3.00) - 面积: 15.00, 周长: 16.00
	// 圆形(半径:4.00) - 面积: 50.27, 周长: 25.13
	// 三角形(边长:3.00,4.00,5.00) - 面积: 6.00, 周长: 12.00
	
	totalArea := calculateTotalArea(shapes)
	fmt.Printf("总面积: %.2f\n", totalArea) // 输出: 总面积: 71.27
	
	// 2. 动物多态
	fmt.Println("\n--- 动物多态 ---")
	
	animals := []Animal{
		Dog{Name: "旺财", Breed: "金毛"},
		Cat{Name: "咪咪", Color: "白色"},
		Bird{Name: "小鸟", Species: "麻雀"},
	}
	
	fmt.Println("动物们的行为:")
	for _, animal := range animals {
		fmt.Println(animal.Speak())
		fmt.Println(animal.Move())
	}
	
	fmt.Println("\n给动物们喂食:")
	feedAnimals(animals, "食物")
	// 输出:
	// 旺财在吃食物
	// 咪咪在优雅地吃食物
	// 小鸟在啄食食物
	
	// 3. 支付方法多态
	fmt.Println("\n--- 支付方法多态 ---")
	
	paymentMethods := []PaymentMethod{
		CreditCard{CardNumber: "1234-5678-9012-3456", CardType: "Visa"},
		PayPal{Email: "<EMAIL>"},
		BankTransfer{BankName: "工商银行", AccountNumber: "6222-0000-0000-0000"},
	}
	
	processPayments(paymentMethods, 100.0)
	
	// 4. 数据存储多态
	fmt.Println("\n--- 数据存储多态 ---")
	
	stores := []DataStore{
		NewMemoryStore(),
		NewFileStore("data.json"),
	}
	
	storeNames := []string{"内存存储", "文件存储"}
	
	for i, store := range stores {
		testDataStore(store, storeNames[i])
	}
	
	// 5. 接口类型断言
	fmt.Println("\n--- 接口类型断言 ---")
	
	var shape Shape = Circle{Radius: 5}
	
	// 类型断言
	if circle, ok := shape.(Circle); ok {
		fmt.Printf("这是一个圆，半径: %.2f\n", circle.Radius) // 输出: 这是一个圆，半径: 5.00
	}
	
	// 类型开关
	switch s := shape.(type) {
	case Rectangle:
		fmt.Printf("矩形: %.2f x %.2f\n", s.Width, s.Height)
	case Circle:
		fmt.Printf("圆形: 半径 %.2f\n", s.Radius) // 输出: 圆形: 半径 5.00
	case Triangle:
		fmt.Printf("三角形: %.2f, %.2f, %.2f\n", s.A, s.B, s.C)
	default:
		fmt.Println("未知图形类型")
	}
	
	// 6. 空接口多态
	fmt.Println("\n--- 空接口多态 ---")
	
	processAnyValue := func(value interface{}) {
		switch v := value.(type) {
		case int:
			fmt.Printf("整数: %d\n", v)
		case string:
			fmt.Printf("字符串: %s\n", v)
		case bool:
			fmt.Printf("布尔值: %t\n", v)
		case Shape:
			fmt.Printf("图形: %s, 面积: %.2f\n", v.String(), v.Area())
		case Animal:
			fmt.Printf("动物: %s\n", v.Speak())
		default:
			fmt.Printf("未知类型: %T, 值: %v\n", v, v)
		}
	}
	
	values := []interface{}{
		42,
		"Hello",
		true,
		Rectangle{Width: 2, Height: 3},
		Dog{Name: "小狗", Breed: "泰迪"},
		[]int{1, 2, 3},
	}
	
	for _, value := range values {
		processAnyValue(value)
	}
	
	// 7. 实际应用：插件系统
	fmt.Println("\n--- 实际应用：插件系统 ---")
	
	type Plugin interface {
		Name() string
		Version() string
		Execute(args map[string]interface{}) (interface{}, error)
	}
	
	type LoggerPlugin struct{}
	
	func (lp LoggerPlugin) Name() string {
		return "Logger"
	}
	
	func (lp LoggerPlugin) Version() string {
		return "1.0.0"
	}
	
	func (lp LoggerPlugin) Execute(args map[string]interface{}) (interface{}, error) {
		message, ok := args["message"].(string)
		if !ok {
			return nil, fmt.Errorf("缺少message参数")
		}
		fmt.Printf("[LOG] %s\n", message)
		return "logged", nil
	}
	
	type CalculatorPlugin struct{}
	
	func (cp CalculatorPlugin) Name() string {
		return "Calculator"
	}
	
	func (cp CalculatorPlugin) Version() string {
		return "2.0.0"
	}
	
	func (cp CalculatorPlugin) Execute(args map[string]interface{}) (interface{}, error) {
		a, aOk := args["a"].(float64)
		b, bOk := args["b"].(float64)
		op, opOk := args["operation"].(string)
		
		if !aOk || !bOk || !opOk {
			return nil, fmt.Errorf("缺少必要参数")
		}
		
		switch op {
		case "+":
			return a + b, nil
		case "-":
			return a - b, nil
		case "*":
			return a * b, nil
		case "/":
			if b == 0 {
				return nil, fmt.Errorf("除数不能为0")
			}
			return a / b, nil
		default:
			return nil, fmt.Errorf("不支持的操作: %s", op)
		}
	}
	
	// 插件管理器
	plugins := []Plugin{
		LoggerPlugin{},
		CalculatorPlugin{},
	}
	
	fmt.Println("已加载的插件:")
	for _, plugin := range plugins {
		fmt.Printf("- %s v%s\n", plugin.Name(), plugin.Version())
	}
	
	// 使用插件
	for _, plugin := range plugins {
		fmt.Printf("\n执行插件 %s:\n", plugin.Name())
		
		var args map[string]interface{}
		var result interface{}
		var err error
		
		switch plugin.Name() {
		case "Logger":
			args = map[string]interface{}{
				"message": "这是一条日志消息",
			}
		case "Calculator":
			args = map[string]interface{}{
				"a":         10.0,
				"b":         3.0,
				"operation": "+",
			}
		}
		
		result, err = plugin.Execute(args)
		if err != nil {
			fmt.Printf("执行失败: %v\n", err)
		} else {
			fmt.Printf("执行结果: %v\n", result)
		}
	}
	
	// 8. 多态的优势总结
	fmt.Println("\n--- 多态的优势 ---")
	
	fmt.Println("Go语言多态的优势:")
	fmt.Println("1. 代码复用：相同的代码可以处理不同类型")
	fmt.Println("2. 扩展性：添加新类型无需修改现有代码")
	fmt.Println("3. 灵活性：运行时决定调用哪个方法")
	fmt.Println("4. 解耦：依赖接口而不是具体实现")
	fmt.Println("5. 测试友好：容易创建mock对象")
	fmt.Println("6. 插件化：支持插件式架构")
}

/*
运行命令: conda activate golang && go run 05_oop/04_polymorphism.go

Go语言多态总结：

1. 多态实现：
   - 通过接口实现多态
   - 隐式接口实现
   - 运行时方法分发

2. 多态特点：
   - 同一接口，不同实现
   - 运行时确定具体类型
   - 代码复用和扩展性

3. 类型断言：
   - value, ok := interface.(Type)
   - 安全的类型转换
   - 检查接口的具体类型

4. 类型开关：
   - switch v := i.(type)
   - 根据类型执行不同逻辑
   - 处理多种类型

5. 空接口：
   - interface{} 可以存储任何类型
   - 需要类型断言获取具体值
   - 类似其他语言的Object

6. 应用场景：
   - 策略模式
   - 工厂模式
   - 插件系统
   - 数据处理管道
   - 测试mock

7. 最佳实践：
   - 接口定义在使用方
   - 接口应该小而专注
   - 优先使用组合
   - 合理使用类型断言
   - 避免过度抽象

8. 优势：
   - 代码复用
   - 易于扩展
   - 降低耦合
   - 提高灵活性
   - 支持插件化架构
*/
