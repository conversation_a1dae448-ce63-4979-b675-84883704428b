// 01_package_basics.go - Go语言包基础
// 这个文件演示了Go语言中包的基本概念和使用

package main

import (
	"fmt"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

// 1. 包的基本概念演示
func packageBasicsDemo() {
	fmt.Println("--- 包的基本概念 ---")
	
	// 使用标准库包
	fmt.Println("使用fmt包进行格式化输出")
	
	// 使用math包
	fmt.Printf("圆周率: %.6f\n", math.Pi)                    // 输出: 圆周率: 3.141593
	fmt.Printf("平方根: %.2f\n", math.Sqrt(16))              // 输出: 平方根: 4.00
	fmt.Printf("幂运算: %.0f\n", math.Pow(2, 8))             // 输出: 幂运算: 256
	
	// 使用time包
	now := time.Now()
	fmt.Printf("当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	
	// 使用strings包
	text := "Hello, Go Programming!"
	fmt.Printf("转大写: %s\n", strings.ToUpper(text))
	fmt.Printf("包含检查: %t\n", strings.Contains(text, "Go"))
	fmt.Printf("分割字符串: %v\n", strings.Split(text, " "))
}

// 2. 包的导入方式演示
func importStylesDemo() {
	fmt.Println("\n--- 包的导入方式 ---")
	
	// 1. 标准导入
	fmt.Println("1. 标准导入: import \"fmt\"")
	
	// 2. 别名导入（在文件顶部已演示）
	fmt.Println("2. 别名导入: import f \"fmt\"")
	
	// 3. 点导入（不推荐，容易造成命名冲突）
	fmt.Println("3. 点导入: import . \"fmt\" (不推荐)")
	
	// 4. 空白导入（只执行包的init函数）
	fmt.Println("4. 空白导入: import _ \"package\" (用于副作用)")
	
	// 演示包的使用
	fmt.Printf("字符串转整数: %d\n", mustAtoi("123"))
	fmt.Printf("随机数: %d\n", rand.Intn(100))
}

// 辅助函数
func mustAtoi(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		panic(err)
	}
	return i
}

// 3. 标准库常用包演示
func standardLibraryDemo() {
	fmt.Println("\n--- 标准库常用包演示 ---")
	
	// strings包
	fmt.Println("strings包:")
	text := "  Go Programming Language  "
	fmt.Printf("  原文: '%s'\n", text)
	fmt.Printf("  去空格: '%s'\n", strings.TrimSpace(text))
	fmt.Printf("  替换: '%s'\n", strings.ReplaceAll(text, "Go", "Golang"))
	fmt.Printf("  重复: '%s'\n", strings.Repeat("Go ", 3))
	
	// strconv包
	fmt.Println("\nstrconv包:")
	fmt.Printf("  字符串转整数: %d\n", mustAtoi("42"))
	fmt.Printf("  整数转字符串: %s\n", strconv.Itoa(42))
	fmt.Printf("  布尔转字符串: %s\n", strconv.FormatBool(true))
	if b, err := strconv.ParseBool("true"); err == nil {
		fmt.Printf("  字符串转布尔: %t\n", b)
	}
	
	// sort包
	fmt.Println("\nsort包:")
	numbers := []int{64, 34, 25, 12, 22, 11, 90}
	fmt.Printf("  排序前: %v\n", numbers)
	sort.Ints(numbers)
	fmt.Printf("  排序后: %v\n", numbers)
	
	words := []string{"banana", "apple", "cherry", "date"}
	fmt.Printf("  字符串排序前: %v\n", words)
	sort.Strings(words)
	fmt.Printf("  字符串排序后: %v\n", words)
	
	// os包
	fmt.Println("\nos包:")
	fmt.Printf("  当前工作目录: %s\n", getCurrentDir())
	fmt.Printf("  环境变量PATH存在: %t\n", os.Getenv("PATH") != "")
	fmt.Printf("  程序参数: %v\n", os.Args)
	
	// filepath包
	fmt.Println("\nfilepath包:")
	path := "/home/<USER>/documents/file.txt"
	fmt.Printf("  路径: %s\n", path)
	fmt.Printf("  目录: %s\n", filepath.Dir(path))
	fmt.Printf("  文件名: %s\n", filepath.Base(path))
	fmt.Printf("  扩展名: %s\n", filepath.Ext(path))
	
	// time包
	fmt.Println("\ntime包:")
	now := time.Now()
	fmt.Printf("  当前时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("  Unix时间戳: %d\n", now.Unix())
	fmt.Printf("  一小时后: %s\n", now.Add(time.Hour).Format("15:04:05"))
	
	// 时间解析
	if t, err := time.Parse("2006-01-02", "2023-12-25"); err == nil {
		fmt.Printf("  解析时间: %s\n", t.Format("2006年01月02日"))
	}
}

func getCurrentDir() string {
	dir, err := os.Getwd()
	if err != nil {
		return "获取失败"
	}
	return dir
}

// 4. 包的可见性演示
func visibilityDemo() {
	fmt.Println("\n--- 包的可见性演示 ---")
	
	fmt.Println("Go语言包的可见性规则:")
	fmt.Println("1. 大写字母开头的标识符是公开的(exported)")
	fmt.Println("2. 小写字母开头的标识符是私有的(unexported)")
	fmt.Println("3. 公开的标识符可以被其他包访问")
	fmt.Println("4. 私有的标识符只能在包内访问")
	
	// 示例结构体
	type PublicStruct struct {
		PublicField  string // 公开字段
		privateField string // 私有字段
	}
	
	// 公开方法
	func (ps PublicStruct) PublicMethod() string {
		return "这是公开方法"
	}
	
	// 私有方法
	func (ps PublicStruct) privateMethod() string {
		return "这是私有方法"
	}
	
	ps := PublicStruct{
		PublicField:  "公开字段值",
		privateField: "私有字段值",
	}
	
	fmt.Printf("公开字段: %s\n", ps.PublicField)
	fmt.Printf("公开方法: %s\n", ps.PublicMethod())
	// 注意：私有字段和方法在其他包中无法访问
}

// 5. 包的初始化演示
func initializationDemo() {
	fmt.Println("\n--- 包的初始化演示 ---")
	
	fmt.Println("包的初始化顺序:")
	fmt.Println("1. 导入的包先初始化")
	fmt.Println("2. 包级别变量按声明顺序初始化")
	fmt.Println("3. init()函数按出现顺序执行")
	fmt.Println("4. main()函数最后执行")
	
	// 模拟包级别变量初始化
	var packageVar1 = initVar("包变量1")
	var packageVar2 = initVar("包变量2")
	
	fmt.Printf("包变量1: %s\n", packageVar1)
	fmt.Printf("包变量2: %s\n", packageVar2)
}

func initVar(name string) string {
	fmt.Printf("初始化 %s\n", name)
	return name + " 已初始化"
}

// 模拟init函数（实际的init函数会在包加载时自动执行）
func simulateInit() {
	fmt.Println("执行init函数...")
}

// 6. 包的组织和命名演示
func packageOrganizationDemo() {
	fmt.Println("\n--- 包的组织和命名 ---")
	
	fmt.Println("包的命名规范:")
	fmt.Println("1. 使用小写字母")
	fmt.Println("2. 简短且有意义")
	fmt.Println("3. 避免下划线和驼峰命名")
	fmt.Println("4. 包名通常是目录名")
	
	fmt.Println("\n包的组织结构:")
	fmt.Println("project/")
	fmt.Println("├── main.go")
	fmt.Println("├── go.mod")
	fmt.Println("├── internal/")
	fmt.Println("│   └── config/")
	fmt.Println("│       └── config.go")
	fmt.Println("├── pkg/")
	fmt.Println("│   └── utils/")
	fmt.Println("│       └── utils.go")
	fmt.Println("└── cmd/")
	fmt.Println("    └── server/")
	fmt.Println("        └── main.go")
	
	fmt.Println("\n特殊目录:")
	fmt.Println("- internal/: 内部包，不能被外部导入")
	fmt.Println("- pkg/: 可以被外部导入的库代码")
	fmt.Println("- cmd/: 应用程序入口点")
	fmt.Println("- vendor/: 依赖包的本地副本")
}

// 7. 常用标准库包总览
func standardLibraryOverview() {
	fmt.Println("\n--- 常用标准库包总览 ---")
	
	packages := map[string]string{
		"fmt":        "格式化I/O",
		"strings":    "字符串操作",
		"strconv":    "字符串转换",
		"math":       "数学函数",
		"time":       "时间处理",
		"os":         "操作系统接口",
		"io":         "I/O原语",
		"bufio":      "缓冲I/O",
		"bytes":      "字节操作",
		"sort":       "排序算法",
		"regexp":     "正则表达式",
		"encoding":   "编码转换",
		"json":       "JSON处理",
		"xml":        "XML处理",
		"net":        "网络编程",
		"net/http":   "HTTP客户端和服务器",
		"database":   "数据库接口",
		"crypto":     "加密算法",
		"hash":       "哈希函数",
		"compress":   "压缩算法",
		"archive":    "归档格式",
		"path":       "路径操作",
		"filepath":   "文件路径操作",
		"flag":       "命令行参数解析",
		"log":        "日志记录",
		"testing":    "测试框架",
		"reflect":    "反射",
		"unsafe":     "不安全操作",
		"sync":       "同步原语",
		"context":    "上下文",
		"runtime":    "运行时信息",
	}
	
	fmt.Println("Go标准库主要包:")
	for pkg, desc := range packages {
		fmt.Printf("  %-12s: %s\n", pkg, desc)
	}
}

// 8. 实际应用示例
func practicalExample() {
	fmt.Println("\n--- 实际应用示例 ---")
	
	// 文件处理示例
	fmt.Println("文件处理示例:")
	filename := "example.txt"
	fmt.Printf("文件名: %s\n", filename)
	fmt.Printf("扩展名: %s\n", filepath.Ext(filename))
	fmt.Printf("无扩展名: %s\n", strings.TrimSuffix(filename, filepath.Ext(filename)))
	
	// 数据处理示例
	fmt.Println("\n数据处理示例:")
	data := []string{"apple", "banana", "cherry", "date", "elderberry"}
	fmt.Printf("原始数据: %v\n", data)
	
	// 过滤长度大于5的字符串
	var filtered []string
	for _, item := range data {
		if len(item) > 5 {
			filtered = append(filtered, item)
		}
	}
	fmt.Printf("过滤后: %v\n", filtered)
	
	// 转换为大写并排序
	var processed []string
	for _, item := range filtered {
		processed = append(processed, strings.ToUpper(item))
	}
	sort.Strings(processed)
	fmt.Printf("处理后: %v\n", processed)
	
	// 时间处理示例
	fmt.Println("\n时间处理示例:")
	start := time.Now()
	time.Sleep(10 * time.Millisecond) // 模拟处理时间
	elapsed := time.Since(start)
	fmt.Printf("处理耗时: %v\n", elapsed)
}

func main() {
	fmt.Println("=== Go语言包基础示例 ===")
	
	// 1. 包的基本概念
	packageBasicsDemo()
	
	// 2. 包的导入方式
	importStylesDemo()
	
	// 3. 标准库常用包
	standardLibraryDemo()
	
	// 4. 包的可见性
	visibilityDemo()
	
	// 5. 包的初始化
	initializationDemo()
	simulateInit()
	
	// 6. 包的组织和命名
	packageOrganizationDemo()
	
	// 7. 标准库包总览
	standardLibraryOverview()
	
	// 8. 实际应用示例
	practicalExample()
	
	// 9. 最佳实践
	fmt.Println("\n--- 包使用最佳实践 ---")
	
	fmt.Println("包使用最佳实践:")
	fmt.Println("1. 导入需要的包，避免未使用的导入")
	fmt.Println("2. 使用有意义的包别名")
	fmt.Println("3. 避免循环导入")
	fmt.Println("4. 合理组织包结构")
	fmt.Println("5. 遵循包命名规范")
	fmt.Println("6. 使用internal包保护内部实现")
	fmt.Println("7. 编写包文档和示例")
	fmt.Println("8. 保持包的单一职责")
	fmt.Println("9. 优先使用标准库")
	fmt.Println("10. 谨慎使用第三方包")
}

/*
运行命令: conda activate golang && go run 08_packages/01_package_basics.go

Go语言包基础总结：

1. 包的概念：
   - 包是Go代码的组织单位
   - 每个Go文件都属于一个包
   - main包是程序入口点
   - 包名通常与目录名相同

2. 导入方式：
   - 标准导入: import "package"
   - 别名导入: import alias "package"
   - 点导入: import . "package" (不推荐)
   - 空白导入: import _ "package" (副作用)

3. 可见性规则：
   - 大写开头：公开(exported)
   - 小写开头：私有(unexported)
   - 只有公开的标识符可以跨包访问

4. 包的初始化：
   - 导入包先初始化
   - 包级变量按声明顺序初始化
   - init()函数按出现顺序执行
   - main()函数最后执行

5. 标准库：
   - fmt: 格式化I/O
   - strings: 字符串操作
   - strconv: 类型转换
   - time: 时间处理
   - os: 操作系统接口
   - math: 数学函数
   - sort: 排序算法

6. 包的组织：
   - 使用有意义的包名
   - 保持包的单一职责
   - 合理的目录结构
   - internal包用于内部实现

7. 最佳实践：
   - 避免循环导入
   - 使用标准库优先
   - 编写包文档
   - 遵循命名规范
   - 保持包的简洁性

8. 特殊目录：
   - internal/: 内部包
   - pkg/: 库代码
   - cmd/: 应用入口
   - vendor/: 依赖副本
*/
