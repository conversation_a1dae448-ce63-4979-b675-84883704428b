// 02_modules.go - Go语言模块管理
// 这个文件演示了Go模块系统的概念和使用

package main

import (
	"fmt"
	"go/build"
	"os"
	"path/filepath"
	"runtime"
)

// 1. 模块基本概念演示
func moduleBasicsDemo() {
	fmt.Println("--- Go模块基本概念 ---")
	
	fmt.Println("Go模块系统特点:")
	fmt.Println("1. Go 1.11引入的依赖管理系统")
	fmt.Println("2. 替代了GOPATH模式")
	fmt.Println("3. 支持语义化版本控制")
	fmt.Println("4. 自动下载和管理依赖")
	fmt.Println("5. 支持私有模块和代理")
	
	fmt.Println("\n核心文件:")
	fmt.Println("- go.mod: 模块定义文件")
	fmt.Println("- go.sum: 依赖校验文件")
	
	// 显示当前Go环境信息
	fmt.Printf("\nGo版本: %s\n", runtime.Version())
	fmt.Printf("GOOS: %s\n", runtime.GOOS)
	fmt.Printf("GOARCH: %s\n", runtime.GOARCH)
	
	// 显示Go环境变量
	gopath := os.Getenv("GOPATH")
	goroot := os.Getenv("GOROOT")
	goproxy := os.Getenv("GOPROXY")
	gomod := os.Getenv("GO111MODULE")
	
	fmt.Printf("GOPATH: %s\n", gopath)
	fmt.Printf("GOROOT: %s\n", goroot)
	fmt.Printf("GOPROXY: %s\n", goproxy)
	fmt.Printf("GO111MODULE: %s\n", gomod)
}

// 2. go.mod文件结构演示
func goModStructureDemo() {
	fmt.Println("\n--- go.mod文件结构 ---")
	
	fmt.Println("go.mod文件示例:")
	fmt.Println(`
module github.com/username/project

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/go-redis/redis/v8 v8.11.5
    golang.org/x/crypto v0.10.0
)

require (
    // 间接依赖
    github.com/bytedance/sonic v1.9.1 // indirect
    github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
)

exclude (
    github.com/old/package v1.0.0
)

replace (
    github.com/old/package => github.com/new/package v2.0.0
    github.com/local/package => ./local/package
)

retract (
    v1.0.1 // 撤回有问题的版本
)
`)
	
	fmt.Println("go.mod指令说明:")
	fmt.Println("- module: 定义模块路径")
	fmt.Println("- go: 指定Go版本")
	fmt.Println("- require: 声明依赖")
	fmt.Println("- exclude: 排除特定版本")
	fmt.Println("- replace: 替换依赖")
	fmt.Println("- retract: 撤回版本")
}

// 3. 模块命令演示
func moduleCommandsDemo() {
	fmt.Println("\n--- Go模块命令 ---")
	
	commands := map[string]string{
		"go mod init <module>":     "初始化新模块",
		"go mod tidy":              "整理依赖，移除未使用的依赖",
		"go mod download":          "下载依赖到本地缓存",
		"go mod verify":            "验证依赖的完整性",
		"go mod graph":             "打印模块依赖图",
		"go mod why <package>":     "解释为什么需要某个包",
		"go mod edit":              "编辑go.mod文件",
		"go mod vendor":            "将依赖复制到vendor目录",
		"go list -m all":           "列出所有模块",
		"go list -m -versions <m>": "列出模块的所有版本",
		"go get <package>":         "添加或更新依赖",
		"go get -u":                "更新所有依赖",
		"go get <package>@version": "获取特定版本",
		"go get <package>@latest":  "获取最新版本",
	}
	
	fmt.Println("常用Go模块命令:")
	for cmd, desc := range commands {
		fmt.Printf("  %-25s: %s\n", cmd, desc)
	}
}

// 4. 版本管理演示
func versionManagementDemo() {
	fmt.Println("\n--- 版本管理 ---")
	
	fmt.Println("语义化版本控制 (SemVer):")
	fmt.Println("格式: MAJOR.MINOR.PATCH")
	fmt.Println("- MAJOR: 不兼容的API变更")
	fmt.Println("- MINOR: 向后兼容的功能新增")
	fmt.Println("- PATCH: 向后兼容的问题修复")
	
	fmt.Println("\n版本示例:")
	versions := []struct {
		version string
		desc    string
	}{
		{"v1.0.0", "第一个稳定版本"},
		{"v1.1.0", "新增功能，向后兼容"},
		{"v1.1.1", "修复bug，向后兼容"},
		{"v2.0.0", "重大更新，可能不兼容"},
		{"v1.2.0-beta.1", "预发布版本"},
		{"v1.2.0-rc.1", "发布候选版本"},
	}
	
	for _, v := range versions {
		fmt.Printf("  %-15s: %s\n", v.version, v.desc)
	}
	
	fmt.Println("\n版本选择规则:")
	fmt.Println("- 默认选择最新的兼容版本")
	fmt.Println("- 主版本号必须匹配")
	fmt.Println("- 选择最高的次版本号和补丁版本号")
	fmt.Println("- 预发布版本需要明确指定")
}

// 5. 依赖管理最佳实践
func dependencyBestPracticesDemo() {
	fmt.Println("\n--- 依赖管理最佳实践 ---")
	
	fmt.Println("依赖管理原则:")
	fmt.Println("1. 最小化依赖")
	fmt.Println("   - 只添加必要的依赖")
	fmt.Println("   - 定期审查和清理未使用的依赖")
	fmt.Println("   - 优先使用标准库")
	
	fmt.Println("\n2. 版本固定")
	fmt.Println("   - 在生产环境中固定依赖版本")
	fmt.Println("   - 使用go.sum确保依赖完整性")
	fmt.Println("   - 谨慎更新主版本")
	
	fmt.Println("\n3. 安全考虑")
	fmt.Println("   - 定期更新依赖以获取安全补丁")
	fmt.Println("   - 使用go mod verify验证依赖")
	fmt.Println("   - 审查第三方依赖的安全性")
	
	fmt.Println("\n4. 性能优化")
	fmt.Println("   - 使用go mod download预下载依赖")
	fmt.Println("   - 配置GOPROXY加速下载")
	fmt.Println("   - 使用vendor目录离线构建")
}

// 6. 模块发布演示
func modulePublishingDemo() {
	fmt.Println("\n--- 模块发布 ---")
	
	fmt.Println("发布Go模块的步骤:")
	fmt.Println("1. 创建模块")
	fmt.Println("   go mod init github.com/username/module")
	
	fmt.Println("\n2. 编写代码和测试")
	fmt.Println("   - 编写包代码")
	fmt.Println("   - 添加测试")
	fmt.Println("   - 编写文档")
	
	fmt.Println("\n3. 版本标记")
	fmt.Println("   git tag v1.0.0")
	fmt.Println("   git push origin v1.0.0")
	
	fmt.Println("\n4. 发布到代理")
	fmt.Println("   - 推送到Git仓库")
	fmt.Println("   - 模块会自动被Go代理索引")
	
	fmt.Println("\n模块文档:")
	fmt.Println("- pkg.go.dev会自动生成文档")
	fmt.Println("- 使用godoc注释编写文档")
	fmt.Println("- 提供使用示例")
}

// 7. 私有模块演示
func privateModulesDemo() {
	fmt.Println("\n--- 私有模块 ---")
	
	fmt.Println("私有模块配置:")
	fmt.Println("1. 设置GOPRIVATE环境变量")
	fmt.Println("   export GOPRIVATE=github.com/company/*")
	
	fmt.Println("\n2. 配置Git认证")
	fmt.Println("   - 使用SSH密钥")
	fmt.Println("   - 配置.netrc文件")
	fmt.Println("   - 使用Git凭据管理器")
	
	fmt.Println("\n3. 企业代理")
	fmt.Println("   - 搭建私有Go代理")
	fmt.Println("   - 配置GOPROXY指向私有代理")
	fmt.Println("   - 使用Athens或其他代理服务")
	
	fmt.Println("\n示例配置:")
	fmt.Println("export GOPRIVATE=*.corp.com,github.com/company")
	fmt.Println("export GOPROXY=https://proxy.corp.com,direct")
	fmt.Println("export GOSUMDB=off")
}

// 8. 工作区模式演示
func workspaceDemo() {
	fmt.Println("\n--- 工作区模式 (Go 1.18+) ---")
	
	fmt.Println("工作区模式特点:")
	fmt.Println("- 同时开发多个相关模块")
	fmt.Println("- 本地模块替换")
	fmt.Println("- 简化多模块开发")
	
	fmt.Println("\ngo.work文件示例:")
	fmt.Println(`
go 1.21

use (
    ./module1
    ./module2
    ./shared
)

replace github.com/external/module => ./local/module
`)
	
	fmt.Println("\n工作区命令:")
	fmt.Println("go work init ./module1 ./module2  # 初始化工作区")
	fmt.Println("go work use ./module3             # 添加模块到工作区")
	fmt.Println("go work edit -dropdirectory=./old # 移除模块")
	fmt.Println("go work sync                      # 同步工作区")
}

// 9. 实际项目结构演示
func projectStructureDemo() {
	fmt.Println("\n--- 实际项目结构 ---")
	
	fmt.Println("典型Go项目结构:")
	fmt.Println(`
myproject/
├── go.mod
├── go.sum
├── README.md
├── Makefile
├── .gitignore
├── cmd/
│   ├── server/
│   │   └── main.go
│   └── client/
│       └── main.go
├── internal/
│   ├── config/
│   │   └── config.go
│   ├── handler/
│   │   └── handler.go
│   └── service/
│       └── service.go
├── pkg/
│   └── utils/
│       └── utils.go
├── api/
│   └── openapi.yaml
├── web/
│   ├── static/
│   └── templates/
├── scripts/
│   └── build.sh
├── docs/
│   └── README.md
└── test/
    └── integration/
        └── test.go
`)
	
	fmt.Println("目录说明:")
	fmt.Println("- cmd/: 应用程序入口点")
	fmt.Println("- internal/: 私有应用代码")
	fmt.Println("- pkg/: 可被外部使用的库代码")
	fmt.Println("- api/: API定义文件")
	fmt.Println("- web/: Web应用资源")
	fmt.Println("- scripts/: 构建和部署脚本")
	fmt.Println("- docs/: 项目文档")
	fmt.Println("- test/: 额外的测试文件")
}

// 10. 获取当前模块信息
func getCurrentModuleInfo() {
	fmt.Println("\n--- 当前模块信息 ---")
	
	// 获取当前工作目录
	pwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("获取工作目录失败: %v\n", err)
		return
	}
	
	fmt.Printf("当前目录: %s\n", pwd)
	
	// 查找go.mod文件
	goModPath := findGoMod(pwd)
	if goModPath != "" {
		fmt.Printf("go.mod位置: %s\n", goModPath)
	} else {
		fmt.Println("未找到go.mod文件")
	}
	
	// 显示Go构建信息
	ctx := build.Default
	fmt.Printf("GOROOT: %s\n", ctx.GOROOT)
	fmt.Printf("GOPATH: %s\n", ctx.GOPATH)
	fmt.Printf("构建标签: %v\n", ctx.BuildTags)
}

func findGoMod(dir string) string {
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return goModPath
		}
		
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	return ""
}

func main() {
	fmt.Println("=== Go语言模块管理示例 ===")
	
	// 1. 模块基本概念
	moduleBasicsDemo()
	
	// 2. go.mod文件结构
	goModStructureDemo()
	
	// 3. 模块命令
	moduleCommandsDemo()
	
	// 4. 版本管理
	versionManagementDemo()
	
	// 5. 依赖管理最佳实践
	dependencyBestPracticesDemo()
	
	// 6. 模块发布
	modulePublishingDemo()
	
	// 7. 私有模块
	privateModulesDemo()
	
	// 8. 工作区模式
	workspaceDemo()
	
	// 9. 项目结构
	projectStructureDemo()
	
	// 10. 当前模块信息
	getCurrentModuleInfo()
	
	// 11. 总结
	fmt.Println("\n--- Go模块系统总结 ---")
	
	fmt.Println("Go模块系统优势:")
	fmt.Println("1. 摆脱GOPATH限制")
	fmt.Println("2. 语义化版本控制")
	fmt.Println("3. 自动依赖管理")
	fmt.Println("4. 构建可重现性")
	fmt.Println("5. 支持私有模块")
	fmt.Println("6. 代理和镜像支持")
	fmt.Println("7. 安全性验证")
	fmt.Println("8. 工作区模式支持")
}

/*
运行命令: conda activate golang && go run 08_packages/02_modules.go

Go语言模块管理总结：

1. 模块系统：
   - Go 1.11引入的依赖管理系统
   - 替代GOPATH模式
   - 基于语义化版本控制
   - 支持自动依赖管理

2. 核心文件：
   - go.mod: 模块定义和依赖声明
   - go.sum: 依赖校验和安全验证
   - go.work: 工作区配置(Go 1.18+)

3. 主要命令：
   - go mod init: 初始化模块
   - go mod tidy: 整理依赖
   - go get: 添加/更新依赖
   - go mod download: 下载依赖
   - go mod verify: 验证依赖

4. 版本管理：
   - 语义化版本: MAJOR.MINOR.PATCH
   - 版本选择: 最新兼容版本
   - 预发布版本: alpha, beta, rc
   - 版本约束: 主版本兼容性

5. 依赖管理：
   - 最小化依赖原则
   - 版本固定和安全更新
   - 依赖完整性验证
   - 私有模块支持

6. 项目结构：
   - cmd/: 应用入口
   - internal/: 私有代码
   - pkg/: 公共库
   - 标准目录布局

7. 最佳实践：
   - 定期运行go mod tidy
   - 使用go.sum验证依赖
   - 合理组织项目结构
   - 编写清晰的模块文档
   - 遵循语义化版本控制

8. 高级特性：
   - 工作区模式
   - 模块代理和镜像
   - 私有模块配置
   - 依赖替换和排除
*/
