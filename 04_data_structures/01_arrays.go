// 01_arrays.go - Go语言数组
// 这个文件演示了Go语言中数组的定义、初始化和使用

package main

import "fmt"

func main() {
	fmt.Println("=== Go语言数组示例 ===")
	
	// 1. 数组声明和初始化
	fmt.Println("\n--- 数组声明和初始化 ---")
	
	// 声明数组（零值初始化）
	var numbers [5]int
	fmt.Printf("零值数组: %v\n", numbers) // 输出: 零值数组: [0 0 0 0 0]
	
	// 声明并初始化
	var fruits [3]string = [3]string{"苹果", "香蕉", "橙子"}
	fmt.Printf("水果数组: %v\n", fruits) // 输出: 水果数组: [苹果 香蕉 橙子]
	
	// 简化声明
	colors := [4]string{"红色", "绿色", "蓝色", "黄色"}
	fmt.Printf("颜色数组: %v\n", colors) // 输出: 颜色数组: [红色 绿色 蓝色 黄色]
	
	// 让编译器推断数组长度
	scores := [...]int{85, 92, 78, 96, 88}
	fmt.Printf("分数数组: %v (长度: %d)\n", scores, len(scores)) // 输出: 分数数组: [85 92 78 96 88] (长度: 5)
	
	// 2. 数组访问和修改
	fmt.Println("\n--- 数组访问和修改 ---")
	
	// 访问数组元素
	fmt.Printf("第一个水果: %s\n", fruits[0]) // 输出: 第一个水果: 苹果
	fmt.Printf("最后一个水果: %s\n", fruits[len(fruits)-1]) // 输出: 最后一个水果: 橙子
	
	// 修改数组元素
	fruits[1] = "葡萄"
	fmt.Printf("修改后的水果数组: %v\n", fruits) // 输出: 修改后的水果数组: [苹果 葡萄 橙子]
	
	// 3. 数组遍历
	fmt.Println("\n--- 数组遍历 ---")
	
	// 使用传统for循环
	fmt.Print("使用索引遍历分数: ")
	for i := 0; i < len(scores); i++ {
		fmt.Printf("%d ", scores[i]) // 输出: 85 92 78 96 88 
	}
	fmt.Println()
	
	// 使用range遍历（推荐）
	fmt.Print("使用range遍历分数: ")
	for index, score := range scores {
		fmt.Printf("[%d]=%d ", index, score) // 输出: [0]=85 [1]=92 [2]=78 [3]=96 [4]=88 
	}
	fmt.Println()
	
	// 只要值，不要索引
	fmt.Print("只要分数值: ")
	for _, score := range scores {
		fmt.Printf("%d ", score) // 输出: 85 92 78 96 88 
	}
	fmt.Println()
	
	// 4. 多维数组
	fmt.Println("\n--- 多维数组 ---")
	
	// 二维数组
	var matrix [3][3]int = [3][3]int{
		{1, 2, 3},
		{4, 5, 6},
		{7, 8, 9},
	}
	
	fmt.Println("3x3矩阵:")
	for i := 0; i < 3; i++ {
		for j := 0; j < 3; j++ {
			fmt.Printf("%d ", matrix[i][j])
		}
		fmt.Println()
	}
	// 输出:
	// 1 2 3 
	// 4 5 6 
	// 7 8 9 
	
	// 简化的二维数组初始化
	grid := [2][4]string{
		{"A1", "A2", "A3", "A4"},
		{"B1", "B2", "B3", "B4"},
	}
	
	fmt.Println("网格:")
	for _, row := range grid {
		for _, cell := range row {
			fmt.Printf("%-3s ", cell)
		}
		fmt.Println()
	}
	// 输出:
	// A1  A2  A3  A4  
	// B1  B2  B3  B4  
	
	// 5. 数组比较
	fmt.Println("\n--- 数组比较 ---")
	
	arr1 := [3]int{1, 2, 3}
	arr2 := [3]int{1, 2, 3}
	arr3 := [3]int{1, 2, 4}
	
	fmt.Printf("arr1 == arr2: %t\n", arr1 == arr2) // 输出: arr1 == arr2: true
	fmt.Printf("arr1 == arr3: %t\n", arr1 == arr3) // 输出: arr1 == arr3: false
	
	// 注意：不同长度的数组不能比较
	// arr4 := [4]int{1, 2, 3, 4}
	// fmt.Println(arr1 == arr4) // 编译错误
	
	// 6. 数组作为函数参数
	fmt.Println("\n--- 数组作为函数参数 ---")
	
	// 数组是值类型，传递时会复制整个数组
	printArray := func(arr [5]int) {
		fmt.Printf("函数内数组: %v\n", arr)
		arr[0] = 999 // 修改不会影响原数组
		fmt.Printf("函数内修改后: %v\n", arr)
	}
	
	originalArray := [5]int{1, 2, 3, 4, 5}
	fmt.Printf("原数组: %v\n", originalArray)
	printArray(originalArray)
	fmt.Printf("调用后原数组: %v\n", originalArray) // 输出: 调用后原数组: [1 2 3 4 5] (未改变)
	
	// 7. 数组指针
	fmt.Println("\n--- 数组指针 ---")
	
	modifyArrayByPointer := func(arr *[5]int) {
		fmt.Printf("函数内数组指针: %v\n", *arr)
		(*arr)[0] = 888 // 通过指针修改会影响原数组
		fmt.Printf("函数内修改后: %v\n", *arr)
	}
	
	fmt.Printf("修改前原数组: %v\n", originalArray)
	modifyArrayByPointer(&originalArray)
	fmt.Printf("修改后原数组: %v\n", originalArray) // 输出: 修改后原数组: [888 2 3 4 5] (已改变)
	
	// 8. 数组的实际应用
	fmt.Println("\n--- 数组的实际应用 ---")
	
	// 应用1：存储一周的温度
	weekTemperatures := [7]float64{22.5, 25.0, 23.8, 26.2, 24.1, 21.9, 20.3}
	weekdays := [7]string{"周一", "周二", "周三", "周四", "周五", "周六", "周日"}
	
	fmt.Println("一周温度记录:")
	for i, temp := range weekTemperatures {
		fmt.Printf("%s: %.1f°C\n", weekdays[i], temp)
	}
	
	// 计算平均温度
	var sum float64
	for _, temp := range weekTemperatures {
		sum += temp
	}
	average := sum / float64(len(weekTemperatures))
	fmt.Printf("平均温度: %.2f°C\n", average) // 输出: 平均温度: 23.40°C
	
	// 应用2：RGB颜色值
	type RGB [3]int // 定义RGB类型为包含3个int的数组
	
	red := RGB{255, 0, 0}
	green := RGB{0, 255, 0}
	blue := RGB{0, 0, 255}
	
	fmt.Printf("红色RGB: %v\n", red)   // 输出: 红色RGB: [255 0 0]
	fmt.Printf("绿色RGB: %v\n", green) // 输出: 绿色RGB: [0 255 0]
	fmt.Printf("蓝色RGB: %v\n", blue)  // 输出: 蓝色RGB: [0 0 255]
	
	// 应用3：游戏棋盘
	type ChessBoard [8][8]string
	
	var board ChessBoard
	
	// 初始化棋盘
	for i := 0; i < 8; i++ {
		for j := 0; j < 8; j++ {
			if (i+j)%2 == 0 {
				board[i][j] = "⬜"
			} else {
				board[i][j] = "⬛"
			}
		}
	}
	
	fmt.Println("国际象棋棋盘:")
	for _, row := range board {
		for _, cell := range row {
			fmt.Print(cell)
		}
		fmt.Println()
	}
	
	// 9. 数组的限制和注意事项
	fmt.Println("\n--- 数组的限制和注意事项 ---")
	
	fmt.Println("数组特点:")
	fmt.Println("1. 长度固定，编译时确定")
	fmt.Println("2. 长度是类型的一部分：[3]int 和 [4]int 是不同类型")
	fmt.Println("3. 值类型，赋值和传参时会复制整个数组")
	fmt.Println("4. 支持比较操作（相同类型的数组）")
	fmt.Println("5. 零值是所有元素都为对应类型的零值")
	
	// 10. 数组 vs 切片的选择
	fmt.Println("\n--- 数组 vs 切片的选择 ---")
	
	fmt.Println("使用数组的场景:")
	fmt.Println("- 元素数量固定且已知")
	fmt.Println("- 需要值语义（复制行为）")
	fmt.Println("- 性能要求高，避免堆分配")
	fmt.Println("- 作为其他数据结构的基础")
	
	fmt.Println("\n使用切片的场景:")
	fmt.Println("- 元素数量可变")
	fmt.Println("- 需要引用语义")
	fmt.Println("- 需要动态增长")
	fmt.Println("- 大多数实际应用场景")
	
	// 11. 数组的内存布局
	fmt.Println("\n--- 数组的内存布局 ---")
	
	intArray := [5]int{1, 2, 3, 4, 5}
	fmt.Printf("数组地址: %p\n", &intArray)
	fmt.Printf("第一个元素地址: %p\n", &intArray[0])
	fmt.Printf("第二个元素地址: %p\n", &intArray[1])
	
	// 计算元素间的地址差（应该等于int的大小）
	fmt.Printf("元素间地址差: %d 字节\n", 
		uintptr(&intArray[1]) - uintptr(&intArray[0])) // 输出: 元素间地址差: 8 字节 (64位系统)
}

/*
运行命令: go run 04_data_structures/01_arrays.go

Go语言数组总结：

1. 数组特点：
   - 固定长度，编译时确定
   - 长度是类型的一部分
   - 值类型，传递时复制
   - 元素类型相同
   - 内存连续分配

2. 声明语法：
   - var arr [n]T          // 零值初始化
   - var arr [n]T = [n]T{...} // 完整初始化
   - arr := [n]T{...}      // 简化声明
   - arr := [...]T{...}    // 自动推断长度

3. 访问和操作：
   - arr[index] 访问元素
   - len(arr) 获取长度
   - 支持 == 和 != 比较
   - 支持range遍历

4. 多维数组：
   - [m][n]T 二维数组
   - 可以有任意维度
   - 内存按行优先存储

5. 使用场景：
   - 固定大小的数据集合
   - 性能敏感的场景
   - 作为其他数据结构的基础
   - 需要值语义的场景

6. 注意事项：
   - 数组长度不能改变
   - 传递大数组性能较差
   - 不同长度的数组是不同类型
   - 实际开发中更多使用切片
*/
