// 03_maps.go - Go语言映射(Map)
// 这个文件演示了Go语言中映射的定义、操作和使用

package main

import (
	"fmt"
	"sort"
)

func main() {
	fmt.Println("=== Go语言映射(Map)示例 ===")
	
	// 1. 映射的创建
	fmt.Println("\n--- 映射的创建 ---")
	
	// 方式1：使用make函数
	var scores map[string]int
	scores = make(map[string]int)
	fmt.Printf("空映射: %v\n", scores) // 输出: 空映射: map[]
	
	// 方式2：声明并初始化
	ages := map[string]int{
		"张三": 25,
		"李四": 30,
		"王五": 28,
	}
	fmt.Printf("年龄映射: %v\n", ages) // 输出: 年龄映射: map[张三:25 李四:30 王五:28]
	
	// 方式3：make并指定初始容量（可选）
	colors := make(map[string]string, 10)
	colors["red"] = "红色"
	colors["green"] = "绿色"
	colors["blue"] = "蓝色"
	fmt.Printf("颜色映射: %v\n", colors) // 输出: 颜色映射: map[blue:蓝色 green:绿色 red:红色]
	
	// 2. 映射的基本操作
	fmt.Println("\n--- 映射的基本操作 ---")
	
	// 添加/修改元素
	scores["数学"] = 95
	scores["英语"] = 88
	scores["物理"] = 92
	fmt.Printf("添加成绩后: %v\n", scores) // 输出: 添加成绩后: map[数学:95 英语:88 物理:92]
	
	// 访问元素
	mathScore := scores["数学"]
	fmt.Printf("数学成绩: %d\n", mathScore) // 输出: 数学成绩: 95
	
	// 检查键是否存在
	englishScore, exists := scores["英语"]
	if exists {
		fmt.Printf("英语成绩: %d\n", englishScore) // 输出: 英语成绩: 88
	}
	
	// 访问不存在的键
	chemScore, exists := scores["化学"]
	fmt.Printf("化学成绩: %d, 存在: %t\n", chemScore, exists) // 输出: 化学成绩: 0, 存在: false
	
	// 删除元素
	delete(scores, "物理")
	fmt.Printf("删除物理后: %v\n", scores) // 输出: 删除物理后: map[数学:95 英语:88]
	
	// 3. 映射的遍历
	fmt.Println("\n--- 映射的遍历 ---")
	
	students := map[string]int{
		"小明": 85,
		"小红": 92,
		"小刚": 78,
		"小丽": 96,
	}
	
	// 遍历键值对
	fmt.Println("学生成绩:")
	for name, score := range students {
		fmt.Printf("%s: %d分\n", name, score)
	}
	
	// 只遍历键
	fmt.Print("学生姓名: ")
	for name := range students {
		fmt.Printf("%s ", name) // 输出顺序是随机的
	}
	fmt.Println()
	
	// 只遍历值
	fmt.Print("所有分数: ")
	for _, score := range students {
		fmt.Printf("%d ", score) // 输出顺序是随机的
	}
	fmt.Println()
	
	// 4. 映射的零值和nil检查
	fmt.Println("\n--- 映射的零值和nil检查 ---")
	
	var nilMap map[string]int
	fmt.Printf("nil映射: %v\n", nilMap)                    // 输出: nil映射: map[]
	fmt.Printf("nil映射长度: %d\n", len(nilMap))            // 输出: nil映射长度: 0
	fmt.Printf("nil映射是否为nil: %t\n", nilMap == nil)    // 输出: nil映射是否为nil: true
	
	// 从nil映射读取是安全的
	value, ok := nilMap["key"]
	fmt.Printf("从nil映射读取: value=%d, ok=%t\n", value, ok) // 输出: 从nil映射读取: value=0, ok=false
	
	// 但写入nil映射会panic
	// nilMap["key"] = 1 // 这会导致panic
	
	// 5. 映射作为函数参数
	fmt.Println("\n--- 映射作为函数参数 ---")
	
	// 映射是引用类型，传递时不会复制
	modifyMap := func(m map[string]int) {
		m["新增"] = 100
		if val, exists := m["小明"]; exists {
			m["小明"] = val + 10
		}
		fmt.Printf("函数内映射: %v\n", m)
	}
	
	fmt.Printf("修改前: %v\n", students)
	modifyMap(students)
	fmt.Printf("修改后: %v\n", students) // 输出: 原映射被修改了
	
	// 6. 复杂类型作为值
	fmt.Println("\n--- 复杂类型作为值 ---")
	
	// 映射的值可以是切片
	grades := map[string][]int{
		"张三": {85, 92, 78},
		"李四": {90, 88, 95},
		"王五": {82, 86, 91},
	}
	
	fmt.Println("学生各科成绩:")
	for name, scoreList := range grades {
		fmt.Printf("%s: %v\n", name, scoreList)
	}
	
	// 映射的值可以是映射
	cityInfo := map[string]map[string]interface{}{
		"北京": {
			"人口":   2154,
			"面积":   16410,
			"简称":   "京",
			"直辖市": true,
		},
		"上海": {
			"人口":   2428,
			"面积":   6340,
			"简称":   "沪",
			"直辖市": true,
		},
	}
	
	fmt.Println("城市信息:")
	for city, info := range cityInfo {
		fmt.Printf("%s: %v\n", city, info)
	}
	
	// 7. 映射的排序
	fmt.Println("\n--- 映射的排序 ---")
	
	// 映射本身是无序的，但可以对键进行排序后遍历
	wordCount := map[string]int{
		"go":     15,
		"python": 8,
		"java":   12,
		"c++":    6,
		"rust":   4,
	}
	
	// 获取所有键并排序
	var keys []string
	for key := range wordCount {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	
	fmt.Println("按键排序的词频:")
	for _, key := range keys {
		fmt.Printf("%s: %d\n", key, wordCount[key])
	}
	
	// 按值排序（需要自定义排序）
	type KeyValue struct {
		Key   string
		Value int
	}
	
	var kvPairs []KeyValue
	for k, v := range wordCount {
		kvPairs = append(kvPairs, KeyValue{k, v})
	}
	
	// 按值降序排序
	sort.Slice(kvPairs, func(i, j int) bool {
		return kvPairs[i].Value > kvPairs[j].Value
	})
	
	fmt.Println("按值排序的词频:")
	for _, kv := range kvPairs {
		fmt.Printf("%s: %d\n", kv.Key, kv.Value)
	}
	
	// 8. 实际应用：字符统计
	fmt.Println("\n--- 实际应用：字符统计 ---")
	
	text := "hello world"
	charCount := make(map[rune]int)
	
	for _, char := range text {
		charCount[char]++
	}
	
	fmt.Printf("文本 '%s' 的字符统计:\n", text)
	for char, count := range charCount {
		if char == ' ' {
			fmt.Printf("空格: %d\n", count)
		} else {
			fmt.Printf("'%c': %d\n", char, count)
		}
	}
	
	// 9. 实际应用：分组统计
	fmt.Println("\n--- 实际应用：分组统计 ---")
	
	type Person struct {
		Name string
		Age  int
		City string
	}
	
	people := []Person{
		{"张三", 25, "北京"},
		{"李四", 30, "上海"},
		{"王五", 28, "北京"},
		{"赵六", 35, "上海"},
		{"钱七", 22, "广州"},
	}
	
	// 按城市分组
	cityGroups := make(map[string][]Person)
	for _, person := range people {
		cityGroups[person.City] = append(cityGroups[person.City], person)
	}
	
	fmt.Println("按城市分组:")
	for city, group := range cityGroups {
		fmt.Printf("%s: ", city)
		for i, person := range group {
			if i > 0 {
				fmt.Print(", ")
			}
			fmt.Printf("%s(%d岁)", person.Name, person.Age)
		}
		fmt.Println()
	}
	
	// 10. 实际应用：缓存实现
	fmt.Println("\n--- 实际应用：缓存实现 ---")
	
	type Cache struct {
		data map[string]interface{}
	}
	
	// 创建缓存
	newCache := func() *Cache {
		return &Cache{
			data: make(map[string]interface{}),
		}
	}
	
	// 设置缓存
	set := func(c *Cache, key string, value interface{}) {
		c.data[key] = value
	}
	
	// 获取缓存
	get := func(c *Cache, key string) (interface{}, bool) {
		value, exists := c.data[key]
		return value, exists
	}
	
	// 删除缓存
	remove := func(c *Cache, key string) {
		delete(c.data, key)
	}
	
	// 清空缓存
	clear := func(c *Cache) {
		c.data = make(map[string]interface{})
	}
	
	// 测试缓存
	cache := newCache()
	
	// 设置缓存
	set(cache, "user:1", map[string]string{"name": "张三", "email": "<EMAIL>"})
	set(cache, "config:timeout", 30)
	set(cache, "feature:enabled", true)
	
	// 获取缓存
	if user, exists := get(cache, "user:1"); exists {
		fmt.Printf("用户信息: %v\n", user)
	}
	
	if timeout, exists := get(cache, "config:timeout"); exists {
		fmt.Printf("超时配置: %v\n", timeout)
	}
	
	// 检查不存在的键
	if _, exists := get(cache, "nonexistent"); !exists {
		fmt.Println("键不存在")
	}
	
	fmt.Printf("缓存大小: %d\n", len(cache.data))
	
	// 11. 实际应用：索引构建
	fmt.Println("\n--- 实际应用：索引构建 ---")
	
	type Article struct {
		ID    int
		Title string
		Tags  []string
	}
	
	articles := []Article{
		{1, "Go语言入门", []string{"编程", "Go", "教程"}},
		{2, "Python数据分析", []string{"编程", "Python", "数据"}},
		{3, "机器学习基础", []string{"AI", "机器学习", "数据"}},
		{4, "Go并发编程", []string{"编程", "Go", "并发"}},
	}
	
	// 构建标签索引
	tagIndex := make(map[string][]int)
	for _, article := range articles {
		for _, tag := range article.Tags {
			tagIndex[tag] = append(tagIndex[tag], article.ID)
		}
	}
	
	fmt.Println("标签索引:")
	for tag, articleIDs := range tagIndex {
		fmt.Printf("%s: %v\n", tag, articleIDs)
	}
	
	// 根据标签查找文章
	searchTag := "Go"
	if articleIDs, exists := tagIndex[searchTag]; exists {
		fmt.Printf("标签'%s'的文章ID: %v\n", searchTag, articleIDs)
	}
	
	// 12. 映射的性能特点
	fmt.Println("\n--- 映射的性能特点 ---")
	
	fmt.Println("映射性能特点:")
	fmt.Println("1. 平均时间复杂度: O(1) 查找、插入、删除")
	fmt.Println("2. 最坏时间复杂度: O(n) (哈希冲突严重时)")
	fmt.Println("3. 空间复杂度: O(n)")
	fmt.Println("4. 无序存储，遍历顺序随机")
	fmt.Println("5. 键必须是可比较类型")
	
	// 13. 映射的限制和注意事项
	fmt.Println("\n--- 映射的限制和注意事项 ---")
	
	fmt.Println("映射使用注意事项:")
	fmt.Println("1. 映射不是线程安全的")
	fmt.Println("2. 键的类型必须支持==和!=操作")
	fmt.Println("3. 不能使用切片、映射、函数作为键")
	fmt.Println("4. nil映射可以读取但不能写入")
	fmt.Println("5. 映射的零值是nil")
	fmt.Println("6. 遍历顺序是随机的")
	
	// 可以作为键的类型示例
	validKeys := map[interface{}]string{
		42:          "整数键",
		"string":    "字符串键",
		true:        "布尔键",
		3.14:        "浮点键",
		complex(1, 2): "复数键",
	}
	
	fmt.Println("有效的键类型示例:")
	for key, desc := range validKeys {
		fmt.Printf("%v (%T): %s\n", key, key, desc)
	}
}

/*
运行命令: conda activate golang && go run 04_data_structures/03_maps.go

Go语言映射(Map)总结：

1. 映射特点：
   - 键值对集合，类似其他语言的字典/哈希表
   - 引用类型，零值是nil
   - 无序存储，遍历顺序随机
   - 键必须是可比较类型

2. 创建方式：
   - make(map[K]V)：创建空映射
   - map[K]V{k1:v1, k2:v2}：字面量初始化
   - var m map[K]V：声明nil映射

3. 基本操作：
   - m[key] = value：设置键值
   - value := m[key]：获取值
   - value, ok := m[key]：安全获取
   - delete(m, key)：删除键值对
   - len(m)：获取长度

4. 遍历方式：
   - for k, v := range m：遍历键值对
   - for k := range m：只遍历键
   - for _, v := range m：只遍历值

5. 键的要求：
   - 必须支持==和!=操作
   - 可以是：基本类型、数组、结构体、指针
   - 不可以是：切片、映射、函数

6. 常见应用：
   - 缓存系统
   - 索引构建
   - 计数统计
   - 分组聚合
   - 配置管理

7. 性能特点：
   - 平均O(1)时间复杂度
   - 哈希表实现
   - 动态扩容
   - 内存开销相对较大

8. 注意事项：
   - 不是线程安全的
   - nil映射不能写入
   - 遍历顺序随机
   - 大量数据时考虑内存使用
*/
