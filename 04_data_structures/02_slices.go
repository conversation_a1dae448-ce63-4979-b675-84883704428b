// 02_slices.go - Go语言切片
// 这个文件演示了Go语言中切片的定义、操作和使用

package main

import "fmt"

func main() {
	fmt.Println("=== Go语言切片示例 ===")
	
	// 1. 切片的创建
	fmt.Println("\n--- 切片的创建 ---")
	
	// 方式1：从数组创建切片
	array := [5]int{1, 2, 3, 4, 5}
	slice1 := array[1:4] // 从索引1到3（不包括4）
	fmt.Printf("原数组: %v\n", array)      // 输出: 原数组: [1 2 3 4 5]
	fmt.Printf("切片[1:4]: %v\n", slice1)  // 输出: 切片[1:4]: [2 3 4]
	
	// 方式2：直接声明切片
	var slice2 []int // 声明nil切片
	fmt.Printf("nil切片: %v, 长度: %d, 容量: %d\n", slice2, len(slice2), cap(slice2)) // 输出: nil切片: [], 长度: 0, 容量: 0
	
	// 方式3：使用切片字面量
	slice3 := []string{"苹果", "香蕉", "橙子"}
	fmt.Printf("字符串切片: %v\n", slice3) // 输出: 字符串切片: [苹果 香蕉 橙子]
	
	// 方式4：使用make函数
	slice4 := make([]int, 3)    // 长度为3，容量为3
	slice5 := make([]int, 3, 5) // 长度为3，容量为5
	fmt.Printf("make切片1: %v, 长度: %d, 容量: %d\n", slice4, len(slice4), cap(slice4)) // 输出: make切片1: [0 0 0], 长度: 3, 容量: 3
	fmt.Printf("make切片2: %v, 长度: %d, 容量: %d\n", slice5, len(slice5), cap(slice5)) // 输出: make切片2: [0 0 0], 长度: 3, 容量: 5
	
	// 2. 切片的基本操作
	fmt.Println("\n--- 切片的基本操作 ---")
	
	numbers := []int{10, 20, 30, 40, 50}
	fmt.Printf("原切片: %v\n", numbers) // 输出: 原切片: [10 20 30 40 50]
	
	// 访问元素
	fmt.Printf("第一个元素: %d\n", numbers[0])                    // 输出: 第一个元素: 10
	fmt.Printf("最后一个元素: %d\n", numbers[len(numbers)-1])      // 输出: 最后一个元素: 50
	
	// 修改元素
	numbers[1] = 25
	fmt.Printf("修改后: %v\n", numbers) // 输出: 修改后: [10 25 30 40 50]
	
	// 3. 切片的切片操作
	fmt.Println("\n--- 切片的切片操作 ---")
	
	data := []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	fmt.Printf("原数据: %v\n", data) // 输出: 原数据: [0 1 2 3 4 5 6 7 8 9]
	
	// 各种切片操作
	fmt.Printf("data[2:5]: %v\n", data[2:5])   // 输出: data[2:5]: [2 3 4]
	fmt.Printf("data[:3]: %v\n", data[:3])     // 输出: data[:3]: [0 1 2]
	fmt.Printf("data[7:]: %v\n", data[7:])     // 输出: data[7:]: [7 8 9]
	fmt.Printf("data[:]: %v\n", data[:])       // 输出: data[:]: [0 1 2 3 4 5 6 7 8 9]
	
	// 4. append函数
	fmt.Println("\n--- append函数 ---")
	
	fruits := []string{"苹果", "香蕉"}
	fmt.Printf("原水果切片: %v, 长度: %d, 容量: %d\n", fruits, len(fruits), cap(fruits))
	
	// 添加单个元素
	fruits = append(fruits, "橙子")
	fmt.Printf("添加橙子后: %v, 长度: %d, 容量: %d\n", fruits, len(fruits), cap(fruits))
	
	// 添加多个元素
	fruits = append(fruits, "葡萄", "西瓜")
	fmt.Printf("添加多个水果后: %v, 长度: %d, 容量: %d\n", fruits, len(fruits), cap(fruits))
	
	// 添加另一个切片
	moreFruits := []string{"芒果", "菠萝"}
	fruits = append(fruits, moreFruits...)
	fmt.Printf("添加切片后: %v, 长度: %d, 容量: %d\n", fruits, len(fruits), cap(fruits))
	
	// 5. copy函数
	fmt.Println("\n--- copy函数 ---")
	
	source := []int{1, 2, 3, 4, 5}
	destination := make([]int, len(source))
	
	// 复制切片
	copied := copy(destination, source)
	fmt.Printf("源切片: %v\n", source)           // 输出: 源切片: [1 2 3 4 5]
	fmt.Printf("目标切片: %v\n", destination)     // 输出: 目标切片: [1 2 3 4 5]
	fmt.Printf("复制了%d个元素\n", copied)         // 输出: 复制了5个元素
	
	// 修改源切片不影响复制的切片
	source[0] = 100
	fmt.Printf("修改源切片后: %v\n", source)       // 输出: 修改源切片后: [100 2 3 4 5]
	fmt.Printf("目标切片不变: %v\n", destination)   // 输出: 目标切片不变: [1 2 3 4 5]
	
	// 6. 切片的遍历
	fmt.Println("\n--- 切片的遍历 ---")
	
	colors := []string{"红色", "绿色", "蓝色", "黄色"}
	
	// 使用传统for循环
	fmt.Print("传统for循环: ")
	for i := 0; i < len(colors); i++ {
		fmt.Printf("%s ", colors[i]) // 输出: 红色 绿色 蓝色 黄色 
	}
	fmt.Println()
	
	// 使用range（推荐）
	fmt.Print("range遍历: ")
	for index, color := range colors {
		fmt.Printf("[%d]%s ", index, color) // 输出: [0]红色 [1]绿色 [2]蓝色 [3]黄色 
	}
	fmt.Println()
	
	// 只要值
	fmt.Print("只要值: ")
	for _, color := range colors {
		fmt.Printf("%s ", color) // 输出: 红色 绿色 蓝色 黄色 
	}
	fmt.Println()
	
	// 7. 切片作为函数参数
	fmt.Println("\n--- 切片作为函数参数 ---")
	
	// 切片是引用类型，传递时不会复制底层数组
	modifySlice := func(s []int) {
		if len(s) > 0 {
			s[0] = 999 // 修改会影响原切片
		}
		s = append(s, 100) // 但append可能不会影响原切片
		fmt.Printf("函数内切片: %v\n", s)
	}
	
	testSlice := []int{1, 2, 3}
	fmt.Printf("调用前: %v\n", testSlice)
	modifySlice(testSlice)
	fmt.Printf("调用后: %v\n", testSlice) // 输出: 调用后: [999 2 3] (第一个元素被修改，但没有新元素)
	
	// 8. 切片的内存共享
	fmt.Println("\n--- 切片的内存共享 ---")
	
	original := []int{1, 2, 3, 4, 5}
	slice_a := original[1:3]
	slice_b := original[2:4]
	
	fmt.Printf("原切片: %v\n", original) // 输出: 原切片: [1 2 3 4 5]
	fmt.Printf("切片A[1:3]: %v\n", slice_a) // 输出: 切片A[1:3]: [2 3]
	fmt.Printf("切片B[2:4]: %v\n", slice_b) // 输出: 切片B[2:4]: [3 4]
	
	// 修改切片A会影响原切片和切片B
	slice_a[1] = 300
	fmt.Printf("修改切片A后:\n")
	fmt.Printf("原切片: %v\n", original) // 输出: 原切片: [1 2 300 4 5]
	fmt.Printf("切片A: %v\n", slice_a)   // 输出: 切片A: [2 300]
	fmt.Printf("切片B: %v\n", slice_b)   // 输出: 切片B: [300 4]
	
	// 9. 切片的容量和重新分配
	fmt.Println("\n--- 切片的容量和重新分配 ---")
	
	s := make([]int, 0, 3)
	fmt.Printf("初始: len=%d, cap=%d, %v\n", len(s), cap(s), s)
	
	for i := 0; i < 5; i++ {
		s = append(s, i)
		fmt.Printf("添加%d后: len=%d, cap=%d, %v\n", i, len(s), cap(s), s)
	}
	// 观察容量如何增长：0 -> 3 -> 6 -> ...
	
	// 10. 删除切片元素
	fmt.Println("\n--- 删除切片元素 ---")
	
	items := []string{"A", "B", "C", "D", "E"}
	fmt.Printf("原切片: %v\n", items) // 输出: 原切片: [A B C D E]
	
	// 删除索引为2的元素（"C"）
	index := 2
	items = append(items[:index], items[index+1:]...)
	fmt.Printf("删除索引2后: %v\n", items) // 输出: 删除索引2后: [A B D E]
	
	// 删除第一个元素
	items = items[1:]
	fmt.Printf("删除第一个元素后: %v\n", items) // 输出: 删除第一个元素后: [B D E]
	
	// 删除最后一个元素
	items = items[:len(items)-1]
	fmt.Printf("删除最后一个元素后: %v\n", items) // 输出: 删除最后一个元素后: [B D]
	
	// 11. 实际应用：动态数组
	fmt.Println("\n--- 实际应用：动态数组 ---")
	
	// 实现一个简单的动态数组
	type DynamicArray struct {
		data []int
	}
	
	// 添加元素
	add := func(arr *DynamicArray, value int) {
		arr.data = append(arr.data, value)
	}
	
	// 获取元素
	get := func(arr *DynamicArray, index int) (int, bool) {
		if index < 0 || index >= len(arr.data) {
			return 0, false
		}
		return arr.data[index], true
	}
	
	// 删除元素
	remove := func(arr *DynamicArray, index int) bool {
		if index < 0 || index >= len(arr.data) {
			return false
		}
		arr.data = append(arr.data[:index], arr.data[index+1:]...)
		return true
	}
	
	// 测试动态数组
	dynArr := &DynamicArray{}
	
	// 添加元素
	for i := 1; i <= 5; i++ {
		add(dynArr, i*10)
	}
	fmt.Printf("动态数组: %v\n", dynArr.data) // 输出: 动态数组: [10 20 30 40 50]
	
	// 获取元素
	if value, ok := get(dynArr, 2); ok {
		fmt.Printf("索引2的值: %d\n", value) // 输出: 索引2的值: 30
	}
	
	// 删除元素
	remove(dynArr, 1)
	fmt.Printf("删除索引1后: %v\n", dynArr.data) // 输出: 删除索引1后: [10 30 40 50]
	
	// 12. 实际应用：栈实现
	fmt.Println("\n--- 实际应用：栈实现 ---")
	
	type Stack struct {
		items []int
	}
	
	// 入栈
	push := func(s *Stack, item int) {
		s.items = append(s.items, item)
	}
	
	// 出栈
	pop := func(s *Stack) (int, bool) {
		if len(s.items) == 0 {
			return 0, false
		}
		index := len(s.items) - 1
		item := s.items[index]
		s.items = s.items[:index]
		return item, true
	}
	
	// 查看栈顶
	peek := func(s *Stack) (int, bool) {
		if len(s.items) == 0 {
			return 0, false
		}
		return s.items[len(s.items)-1], true
	}
	
	// 测试栈
	stack := &Stack{}
	
	// 入栈
	for i := 1; i <= 3; i++ {
		push(stack, i)
		fmt.Printf("入栈%d: %v\n", i, stack.items)
	}
	
	// 查看栈顶
	if top, ok := peek(stack); ok {
		fmt.Printf("栈顶元素: %d\n", top) // 输出: 栈顶元素: 3
	}
	
	// 出栈
	for len(stack.items) > 0 {
		if item, ok := pop(stack); ok {
			fmt.Printf("出栈%d: %v\n", item, stack.items)
		}
	}
	
	// 13. 切片的最佳实践
	fmt.Println("\n--- 切片的最佳实践 ---")
	
	fmt.Println("切片最佳实践:")
	fmt.Println("1. 预分配容量以提高性能")
	fmt.Println("2. 使用copy避免意外的内存共享")
	fmt.Println("3. 注意切片的零值是nil")
	fmt.Println("4. 大切片使用完后及时释放引用")
	fmt.Println("5. 避免切片内存泄漏")
	
	// 预分配容量示例
	fmt.Println("\n预分配容量示例:")
	
	// 不好的做法：频繁扩容
	var badSlice []int
	for i := 0; i < 1000; i++ {
		badSlice = append(badSlice, i)
	}
	
	// 好的做法：预分配容量
	goodSlice := make([]int, 0, 1000)
	for i := 0; i < 1000; i++ {
		goodSlice = append(goodSlice, i)
	}
	
	fmt.Printf("预分配的切片长度: %d, 容量: %d\n", len(goodSlice), cap(goodSlice))
}

/*
运行命令: go run 04_data_structures/02_slices.go

Go语言切片总结：

1. 切片特点：
   - 动态数组，长度可变
   - 引用类型，指向底层数组
   - 零值是nil
   - 有长度(len)和容量(cap)两个属性

2. 创建方式：
   - 从数组切片：arr[start:end]
   - 切片字面量：[]T{...}
   - make函数：make([]T, len, cap)
   - 声明：var s []T

3. 常用操作：
   - append：添加元素
   - copy：复制切片
   - len：获取长度
   - cap：获取容量

4. 切片操作：
   - s[i]：访问元素
   - s[start:end]：子切片
   - s[:n]：前n个元素
   - s[n:]：从n开始的元素

5. 内存管理：
   - 多个切片可能共享底层数组
   - append可能触发重新分配
   - 容量不足时会自动扩容

6. 最佳实践：
   - 预分配容量提高性能
   - 使用copy避免意外共享
   - 注意内存泄漏问题
   - 合理使用切片操作

7. 常见应用：
   - 动态数组
   - 栈和队列
   - 缓冲区
   - 集合操作
*/
