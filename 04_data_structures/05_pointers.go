// 05_pointers.go - Go语言指针
// 这个文件演示了Go语言中指针的概念、使用和特性

package main

import (
	"fmt"
	"unsafe"
)

func main() {
	fmt.Println("=== Go语言指针示例 ===")
	
	// 1. 指针基础概念
	fmt.Println("\n--- 指针基础概念 ---")
	
	// 声明变量
	var num int = 42
	fmt.Printf("变量num的值: %d\n", num)           // 输出: 变量num的值: 42
	fmt.Printf("变量num的地址: %p\n", &num)        // 输出: 变量num的地址: 0x... (内存地址)
	
	// 声明指针变量
	var ptr *int = &num
	fmt.Printf("指针ptr的值(地址): %p\n", ptr)     // 输出: 指针ptr的值(地址): 0x... (与&num相同)
	fmt.Printf("指针ptr指向的值: %d\n", *ptr)      // 输出: 指针ptr指向的值: 42
	fmt.Printf("指针ptr本身的地址: %p\n", &ptr)    // 输出: 指针ptr本身的地址: 0x... (ptr变量的地址)
	
	// 2. 指针的声明和初始化
	fmt.Println("\n--- 指针的声明和初始化 ---")
	
	// 方式1：先声明后赋值
	var p1 *int
	fmt.Printf("未初始化的指针: %v\n", p1)         // 输出: 未初始化的指针: <nil>
	
	x := 100
	p1 = &x
	fmt.Printf("初始化后的指针: %p, 值: %d\n", p1, *p1) // 输出: 初始化后的指针: 0x..., 值: 100
	
	// 方式2：声明时初始化
	y := 200
	p2 := &y
	fmt.Printf("声明时初始化: %p, 值: %d\n", p2, *p2) // 输出: 声明时初始化: 0x..., 值: 200
	
	// 方式3：使用new函数
	p3 := new(int)
	*p3 = 300
	fmt.Printf("new创建的指针: %p, 值: %d\n", p3, *p3) // 输出: new创建的指针: 0x..., 值: 300
	
	// 3. 指针操作
	fmt.Println("\n--- 指针操作 ---")
	
	value := 50
	pointer := &value
	
	fmt.Printf("原始值: %d\n", value)              // 输出: 原始值: 50
	fmt.Printf("通过指针访问: %d\n", *pointer)      // 输出: 通过指针访问: 50
	
	// 通过指针修改值
	*pointer = 60
	fmt.Printf("通过指针修改后: %d\n", value)       // 输出: 通过指针修改后: 60
	fmt.Printf("指针指向的值: %d\n", *pointer)      // 输出: 指针指向的值: 60
	
	// 4. 指针与函数
	fmt.Println("\n--- 指针与函数 ---")
	
	// 值传递函数
	modifyByValue := func(n int) {
		n = 999
		fmt.Printf("函数内修改值: %d\n", n)
	}
	
	// 指针传递函数
	modifyByPointer := func(n *int) {
		*n = 888
		fmt.Printf("函数内通过指针修改: %d\n", *n)
	}
	
	testNum := 123
	fmt.Printf("修改前: %d\n", testNum)
	
	modifyByValue(testNum)
	fmt.Printf("值传递后: %d\n", testNum)          // 输出: 值传递后: 123 (未改变)
	
	modifyByPointer(&testNum)
	fmt.Printf("指针传递后: %d\n", testNum)        // 输出: 指针传递后: 888 (已改变)
	
	// 5. 指针与数组
	fmt.Println("\n--- 指针与数组 ---")
	
	arr := [5]int{1, 2, 3, 4, 5}
	fmt.Printf("数组: %v\n", arr)
	
	// 数组指针
	arrPtr := &arr
	fmt.Printf("数组指针: %p\n", arrPtr)
	fmt.Printf("通过数组指针访问: %v\n", *arrPtr)
	
	// 修改数组元素
	(*arrPtr)[0] = 10
	fmt.Printf("修改后的数组: %v\n", arr)           // 输出: 修改后的数组: [10 2 3 4 5]
	
	// 元素指针
	elemPtr := &arr[1]
	fmt.Printf("元素指针: %p, 值: %d\n", elemPtr, *elemPtr)
	*elemPtr = 20
	fmt.Printf("修改元素后: %v\n", arr)             // 输出: 修改元素后: [10 20 3 4 5]
	
	// 6. 指针与切片
	fmt.Println("\n--- 指针与切片 ---")
	
	slice := []int{10, 20, 30}
	fmt.Printf("切片: %v\n", slice)
	
	// 切片元素指针
	sliceElemPtr := &slice[1]
	fmt.Printf("切片元素指针: %p, 值: %d\n", sliceElemPtr, *sliceElemPtr)
	*sliceElemPtr = 200
	fmt.Printf("修改后的切片: %v\n", slice)         // 输出: 修改后的切片: [10 200 30]
	
	// 7. 指针与结构体
	fmt.Println("\n--- 指针与结构体 ---")
	
	type Person struct {
		Name string
		Age  int
	}
	
	// 结构体变量
	person1 := Person{Name: "张三", Age: 25}
	fmt.Printf("结构体: %+v\n", person1)
	
	// 结构体指针
	personPtr := &person1
	fmt.Printf("结构体指针: %p\n", personPtr)
	fmt.Printf("通过指针访问: %+v\n", *personPtr)
	
	// Go语言的语法糖：自动解引用
	fmt.Printf("指针访问字段(自动解引用): %s\n", personPtr.Name) // 等价于 (*personPtr).Name
	
	// 修改结构体字段
	personPtr.Age = 26
	fmt.Printf("修改后: %+v\n", person1)           // 输出: 修改后: {Name:张三 Age:26}
	
	// 使用new创建结构体指针
	person2 := new(Person)
	person2.Name = "李四"
	person2.Age = 30
	fmt.Printf("new创建的结构体: %+v\n", *person2)
	
	// 8. 指针与映射
	fmt.Println("\n--- 指针与映射 ---")
	
	m := map[string]int{"a": 1, "b": 2}
	fmt.Printf("映射: %v\n", m)
	
	// 映射指针
	mapPtr := &m
	(*mapPtr)["c"] = 3
	fmt.Printf("通过指针修改映射: %v\n", m)         // 输出: 通过映射指针修改映射: map[a:1 b:2 c:3]
	
	// 注意：不能获取映射元素的地址
	// elemAddr := &m["a"] // 编译错误
	
	// 9. 指针算术（Go语言不支持）
	fmt.Println("\n--- 指针算术说明 ---")
	
	fmt.Println("Go语言不支持指针算术运算:")
	fmt.Println("- 不能对指针进行加减运算")
	fmt.Println("- 不能比较不同类型的指针")
	fmt.Println("- 这样设计是为了内存安全")
	
	// 以下操作在Go中是不允许的：
	// ptr1 := &x
	// ptr2 := ptr1 + 1  // 编译错误
	// ptr3 := ptr1 - 1  // 编译错误
	
	// 10. 指针的零值和nil检查
	fmt.Println("\n--- 指针的零值和nil检查 ---")
	
	var nilPtr *int
	fmt.Printf("nil指针: %v\n", nilPtr)            // 输出: nil指针: <nil>
	fmt.Printf("nil指针是否为nil: %t\n", nilPtr == nil) // 输出: nil指针是否为nil: true
	
	// 安全的指针使用
	if nilPtr != nil {
		fmt.Printf("指针值: %d\n", *nilPtr)
	} else {
		fmt.Println("指针为nil，不能解引用")        // 输出: 指针为nil，不能解引用
	}
	
	// 11. 实际应用：交换两个变量
	fmt.Println("\n--- 实际应用：交换两个变量 ---")
	
	swap := func(a, b *int) {
		*a, *b = *b, *a
	}
	
	x1, y1 := 10, 20
	fmt.Printf("交换前: x1=%d, y1=%d\n", x1, y1)
	swap(&x1, &y1)
	fmt.Printf("交换后: x1=%d, y1=%d\n", x1, y1)   // 输出: 交换后: x1=20, y1=10
	
	// 12. 实际应用：链表节点
	fmt.Println("\n--- 实际应用：链表节点 ---")
	
	type ListNode struct {
		Value int
		Next  *ListNode
	}
	
	// 创建链表节点
	node1 := &ListNode{Value: 1}
	node2 := &ListNode{Value: 2}
	node3 := &ListNode{Value: 3}
	
	// 连接节点
	node1.Next = node2
	node2.Next = node3
	
	// 遍历链表
	fmt.Print("链表: ")
	current := node1
	for current != nil {
		fmt.Printf("%d ", current.Value)
		current = current.Next
	}
	fmt.Println() // 输出: 链表: 1 2 3 
	
	// 13. 实际应用：可选参数
	fmt.Println("\n--- 实际应用：可选参数 ---")
	
	type Config struct {
		Host    string
		Port    int
		Timeout *int // 使用指针表示可选参数
	}
	
	createConfig := func(host string, port int, timeout *int) Config {
		return Config{
			Host:    host,
			Port:    port,
			Timeout: timeout,
		}
	}
	
	// 不提供timeout
	config1 := createConfig("localhost", 8080, nil)
	fmt.Printf("配置1: %+v\n", config1)
	
	// 提供timeout
	timeoutValue := 30
	config2 := createConfig("example.com", 443, &timeoutValue)
	fmt.Printf("配置2: %+v\n", config2)
	
	// 检查可选参数
	if config2.Timeout != nil {
		fmt.Printf("超时设置: %d秒\n", *config2.Timeout) // 输出: 超时设置: 30秒
	}
	
	// 14. 实际应用：工厂函数
	fmt.Println("\n--- 实际应用：工厂函数 ---")
	
	type User struct {
		ID   int
		Name string
		Age  int
	}
	
	// 工厂函数返回指针
	NewUser := func(name string, age int) *User {
		return &User{
			ID:   generateID(), // 假设的ID生成函数
			Name: name,
			Age:  age,
		}
	}
	
	generateID := func() int {
		return 12345 // 简化的ID生成
	}
	
	user := NewUser("新用户", 25)
	fmt.Printf("创建的用户: %+v\n", *user)           // 输出: 创建的用户: {ID:12345 Name:新用户 Age:25}
	
	// 15. 指针的内存管理
	fmt.Println("\n--- 指针的内存管理 ---")
	
	fmt.Println("Go语言的内存管理特点:")
	fmt.Println("1. 自动垃圾回收，无需手动释放内存")
	fmt.Println("2. 栈上分配的变量会自动管理")
	fmt.Println("3. 堆上分配的变量由GC管理")
	fmt.Println("4. 逃逸分析决定变量分配位置")
	
	// 16. 指针的大小
	fmt.Println("\n--- 指针的大小 ---")
	
	var intPtr *int
	var stringPtr *string
	var structPtr *Person
	
	fmt.Printf("int指针大小: %d 字节\n", unsafe.Sizeof(intPtr))       // 输出: 8字节(64位系统)
	fmt.Printf("string指针大小: %d 字节\n", unsafe.Sizeof(stringPtr)) // 输出: 8字节(64位系统)
	fmt.Printf("struct指针大小: %d 字节\n", unsafe.Sizeof(structPtr)) // 输出: 8字节(64位系统)
	
	fmt.Println("所有指针类型的大小都相同（在同一架构上）")
	
	// 17. 指针最佳实践
	fmt.Println("\n--- 指针最佳实践 ---")
	
	fmt.Println("指针使用最佳实践:")
	fmt.Println("1. 使用指针避免大结构体的复制")
	fmt.Println("2. 使用指针实现可选参数")
	fmt.Println("3. 使用指针构建数据结构（链表、树等）")
	fmt.Println("4. 函数需要修改参数时使用指针")
	fmt.Println("5. 始终检查指针是否为nil")
	fmt.Println("6. 避免返回局部变量的指针（Go会自动处理）")
	fmt.Println("7. 合理使用指针，不要过度使用")
	
	// 18. 指针与接口
	fmt.Println("\n--- 指针与接口 ---")
	
	type Printer interface {
		Print()
	}
	
	type Document struct {
		Title string
	}
	
	// 为指针类型实现接口
	printDoc := func(d *Document) {
		fmt.Printf("打印文档: %s\n", d.Title)
	}
	
	doc := &Document{Title: "Go语言指南"}
	printDoc(doc) // 输出: 打印文档: Go语言指南
	
	fmt.Println("\n指针与接口的关系:")
	fmt.Println("- 指针类型和值类型是不同的类型")
	fmt.Println("- 接口可以存储指针或值")
	fmt.Println("- 方法集合决定了类型是否实现接口")
}

/*
运行命令: conda activate golang && go run 04_data_structures/05_pointers.go

Go语言指针总结：

1. 指针基础：
   - 指针存储变量的内存地址
   - 使用&获取地址，使用*解引用
   - 零值是nil

2. 指针操作：
   - &variable：获取变量地址
   - *pointer：获取指针指向的值
   - pointer.field：自动解引用访问字段

3. 指针类型：
   - *T：指向类型T的指针
   - 所有指针类型大小相同（架构相关）

4. 创建指针：
   - &variable：获取现有变量地址
   - new(T)：分配零值并返回指针
   - &T{...}：创建结构体指针

5. 指针特点：
   - 不支持指针算术运算
   - 类型安全，不能随意转换
   - 自动内存管理

6. 使用场景：
   - 避免大对象复制
   - 函数需要修改参数
   - 实现数据结构
   - 可选参数
   - 工厂函数

7. 安全使用：
   - 检查nil指针
   - 避免野指针
   - 合理使用，不过度使用

8. 内存管理：
   - 自动垃圾回收
   - 逃逸分析优化
   - 栈/堆自动选择
*/
