// 04_structs.go - Go语言结构体
// 这个文件演示了Go语言中结构体的定义、使用和特性

package main

import (
	"fmt"
	"time"
	"unsafe"
)

// 1. 基本结构体定义
type Person struct {
	Name string
	Age  int
	City string
}

// 2. 带有不同类型字段的结构体
type Student struct {
	ID       int
	Name     string
	Age      int
	Grades   []float64
	IsActive bool
	Address  Address // 嵌套结构体
}

type Address struct {
	Street   string
	City     string
	Province string
	ZipCode  string
}

// 3. 匿名字段结构体
type Employee struct {
	string // 匿名字段，字段名就是类型名
	int    // 匿名字段
	bool   // 匿名字段
}

// 4. 结构体标签
type User struct {
	ID       int    `json:"id" db:"user_id"`
	Username string `json:"username" db:"username"`
	Email    string `json:"email" db:"email"`
	Created  time.Time `json:"created_at" db:"created_at"`
}

// 5. 空结构体
type Empty struct{}

func main() {
	fmt.Println("=== Go语言结构体示例 ===")
	
	// 1. 结构体的创建和初始化
	fmt.Println("\n--- 结构体的创建和初始化 ---")
	
	// 方式1：零值初始化
	var p1 Person
	fmt.Printf("零值结构体: %+v\n", p1) // 输出: 零值结构体: {Name: Age:0 City:}
	
	// 方式2：字段赋值
	p1.Name = "张三"
	p1.Age = 25
	p1.City = "北京"
	fmt.Printf("赋值后: %+v\n", p1) // 输出: 赋值后: {Name:张三 Age:25 City:北京}
	
	// 方式3：结构体字面量（按字段顺序）
	p2 := Person{"李四", 30, "上海"}
	fmt.Printf("字面量初始化: %+v\n", p2) // 输出: 字面量初始化: {Name:李四 Age:30 City:上海}
	
	// 方式4：结构体字面量（指定字段名）
	p3 := Person{
		Name: "王五",
		Age:  28,
		City: "广州",
	}
	fmt.Printf("指定字段名: %+v\n", p3) // 输出: 指定字段名: {Name:王五 Age:28 City:广州}
	
	// 方式5：部分字段初始化
	p4 := Person{Name: "赵六", Age: 35} // City使用零值
	fmt.Printf("部分初始化: %+v\n", p4) // 输出: 部分初始化: {Name:赵六 Age:35 City:}
	
	// 2. 结构体字段访问
	fmt.Println("\n--- 结构体字段访问 ---")
	
	fmt.Printf("姓名: %s\n", p1.Name) // 输出: 姓名: 张三
	fmt.Printf("年龄: %d\n", p1.Age)  // 输出: 年龄: 25
	fmt.Printf("城市: %s\n", p1.City) // 输出: 城市: 北京
	
	// 修改字段
	p1.Age = 26
	fmt.Printf("修改年龄后: %+v\n", p1) // 输出: 修改年龄后: {Name:张三 Age:26 City:北京}
	
	// 3. 嵌套结构体
	fmt.Println("\n--- 嵌套结构体 ---")
	
	student := Student{
		ID:   1001,
		Name: "小明",
		Age:  20,
		Grades: []float64{85.5, 92.0, 78.5},
		IsActive: true,
		Address: Address{
			Street:   "中山路123号",
			City:     "杭州",
			Province: "浙江",
			ZipCode:  "310000",
		},
	}
	
	fmt.Printf("学生信息: %+v\n", student)
	fmt.Printf("学生地址: %+v\n", student.Address)
	fmt.Printf("学生街道: %s\n", student.Address.Street) // 输出: 学生街道: 中山路123号
	
	// 4. 匿名字段结构体
	fmt.Println("\n--- 匿名字段结构体 ---")
	
	emp := Employee{"张工程师", 5, true}
	fmt.Printf("员工信息: %+v\n", emp) // 输出: 员工信息: {string:张工程师 int:5 bool:true}
	
	// 访问匿名字段
	fmt.Printf("员工姓名: %s\n", emp.string) // 输出: 员工姓名: 张工程师
	fmt.Printf("工作年限: %d\n", emp.int)    // 输出: 工作年限: 5
	fmt.Printf("是否在职: %t\n", emp.bool)   // 输出: 是否在职: true
	
	// 5. 结构体指针
	fmt.Println("\n--- 结构体指针 ---")
	
	// 创建结构体指针
	pp := &Person{Name: "指针人", Age: 40, City: "深圳"}
	fmt.Printf("结构体指针: %+v\n", *pp) // 输出: 结构体指针: {Name:指针人 Age:40 City:深圳}
	
	// 通过指针访问字段（Go会自动解引用）
	fmt.Printf("通过指针访问姓名: %s\n", pp.Name) // 输出: 通过指针访问姓名: 指针人
	
	// 修改指针指向的结构体
	pp.Age = 41
	fmt.Printf("修改后: %+v\n", *pp) // 输出: 修改后: {Name:指针人 Age:41 City:深圳}
	
	// 使用new创建结构体指针
	pNew := new(Person)
	pNew.Name = "新建人"
	pNew.Age = 33
	fmt.Printf("new创建的结构体: %+v\n", *pNew) // 输出: new创建的结构体: {Name:新建人 Age:33 City:}
	
	// 6. 结构体比较
	fmt.Println("\n--- 结构体比较 ---")
	
	person1 := Person{Name: "测试", Age: 25, City: "北京"}
	person2 := Person{Name: "测试", Age: 25, City: "北京"}
	person3 := Person{Name: "测试", Age: 26, City: "北京"}
	
	fmt.Printf("person1 == person2: %t\n", person1 == person2) // 输出: person1 == person2: true
	fmt.Printf("person1 == person3: %t\n", person1 == person3) // 输出: person1 == person3: false
	
	// 注意：包含切片、映射或函数的结构体不能比较
	// student1 := Student{Name: "A", Grades: []float64{90}}
	// student2 := Student{Name: "A", Grades: []float64{90}}
	// fmt.Println(student1 == student2) // 编译错误
	
	// 7. 结构体作为函数参数
	fmt.Println("\n--- 结构体作为函数参数 ---")
	
	// 值传递
	printPersonByValue := func(p Person) {
		fmt.Printf("函数内(值传递): %+v\n", p)
		p.Age = 100 // 修改不会影响原结构体
	}
	
	// 指针传递
	printPersonByPointer := func(p *Person) {
		fmt.Printf("函数内(指针传递): %+v\n", *p)
		p.Age = 200 // 修改会影响原结构体
	}
	
	testPerson := Person{Name: "测试人", Age: 30, City: "测试城市"}
	fmt.Printf("调用前: %+v\n", testPerson)
	
	printPersonByValue(testPerson)
	fmt.Printf("值传递后: %+v\n", testPerson) // 输出: 值传递后: {Name:测试人 Age:30 City:测试城市} (未改变)
	
	printPersonByPointer(&testPerson)
	fmt.Printf("指针传递后: %+v\n", testPerson) // 输出: 指针传递后: {Name:测试人 Age:200 City:测试城市} (已改变)
	
	// 8. 结构体方法（在后续章节详细介绍）
	fmt.Println("\n--- 结构体方法预览 ---")
	
	// 为Person类型定义方法
	introduce := func(p Person) string {
		return fmt.Sprintf("我叫%s，今年%d岁，来自%s", p.Name, p.Age, p.City)
	}
	
	fmt.Println(introduce(person1)) // 输出: 我叫测试，今年25岁，来自北京
	
	// 9. 实际应用：数据建模
	fmt.Println("\n--- 实际应用：数据建模 ---")
	
	// 定义复杂的数据结构
	type Order struct {
		ID          string
		CustomerID  int
		Items       []OrderItem
		TotalAmount float64
		Status      string
		CreatedAt   time.Time
		ShippingAddress Address
	}
	
	type OrderItem struct {
		ProductID int
		Name      string
		Price     float64
		Quantity  int
	}
	
	// 创建订单
	order := Order{
		ID:         "ORD-001",
		CustomerID: 12345,
		Items: []OrderItem{
			{ProductID: 1, Name: "Go语言书籍", Price: 59.9, Quantity: 2},
			{ProductID: 2, Name: "编程键盘", Price: 299.0, Quantity: 1},
		},
		TotalAmount: 418.8,
		Status:      "已支付",
		CreatedAt:   time.Now(),
		ShippingAddress: Address{
			Street:   "科技园路88号",
			City:     "深圳",
			Province: "广东",
			ZipCode:  "518000",
		},
	}
	
	fmt.Printf("订单ID: %s\n", order.ID)
	fmt.Printf("客户ID: %d\n", order.CustomerID)
	fmt.Printf("订单状态: %s\n", order.Status)
	fmt.Printf("总金额: %.2f\n", order.TotalAmount)
	fmt.Printf("商品数量: %d\n", len(order.Items))
	
	fmt.Println("订单商品:")
	for i, item := range order.Items {
		fmt.Printf("  %d. %s - 单价:%.2f 数量:%d\n", 
			i+1, item.Name, item.Price, item.Quantity)
	}
	
	// 10. 实际应用：配置结构体
	fmt.Println("\n--- 实际应用：配置结构体 ---")
	
	type DatabaseConfig struct {
		Host     string
		Port     int
		Username string
		Password string
		Database string
		SSL      bool
		Timeout  time.Duration
	}
	
	type ServerConfig struct {
		Port         int
		Host         string
		ReadTimeout  time.Duration
		WriteTimeout time.Duration
		Database     DatabaseConfig
	}
	
	config := ServerConfig{
		Port:         8080,
		Host:         "localhost",
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		Database: DatabaseConfig{
			Host:     "db.example.com",
			Port:     5432,
			Username: "admin",
			Password: "secret",
			Database: "myapp",
			SSL:      true,
			Timeout:  10 * time.Second,
		},
	}
	
	fmt.Printf("服务器配置: %s:%d\n", config.Host, config.Port)
	fmt.Printf("数据库配置: %s:%d/%s\n", 
		config.Database.Host, config.Database.Port, config.Database.Database)
	
	// 11. 空结构体的使用
	fmt.Println("\n--- 空结构体的使用 ---")
	
	// 空结构体不占用内存空间
	var empty Empty
	fmt.Printf("空结构体大小: %d 字节\n", unsafe.Sizeof(empty)) // 输出: 空结构体大小: 0 字节
	
	// 空结构体常用于信号传递
	done := make(chan Empty)
	go func() {
		time.Sleep(100 * time.Millisecond)
		done <- Empty{} // 发送信号
	}()
	
	<-done // 接收信号
	fmt.Println("任务完成")
	
	// 空结构体用作集合
	set := make(map[string]Empty)
	set["apple"] = Empty{}
	set["banana"] = Empty{}
	set["orange"] = Empty{}
	
	fmt.Print("集合元素: ")
	for fruit := range set {
		fmt.Printf("%s ", fruit) // 输出: apple banana orange (顺序随机)
	}
	fmt.Println()
	
	// 12. 结构体的内存布局
	fmt.Println("\n--- 结构体的内存布局 ---")
	
	type MemoryLayout struct {
		A int8   // 1字节
		B int64  // 8字节
		C int8   // 1字节
	}
	
	var ml MemoryLayout
	fmt.Printf("结构体大小: %d 字节\n", unsafe.Sizeof(ml)) // 可能是24字节（由于内存对齐）
	fmt.Printf("字段A偏移: %d\n", unsafe.Offsetof(ml.A))
	fmt.Printf("字段B偏移: %d\n", unsafe.Offsetof(ml.B))
	fmt.Printf("字段C偏移: %d\n", unsafe.Offsetof(ml.C))
	
	// 13. 结构体最佳实践
	fmt.Println("\n--- 结构体最佳实践 ---")
	
	fmt.Println("结构体最佳实践:")
	fmt.Println("1. 字段名使用驼峰命名法")
	fmt.Println("2. 导出字段首字母大写，私有字段首字母小写")
	fmt.Println("3. 合理安排字段顺序以优化内存对齐")
	fmt.Println("4. 大结构体使用指针传递")
	fmt.Println("5. 使用结构体标签进行元数据标注")
	fmt.Println("6. 避免在结构体中嵌套过深")
	fmt.Println("7. 考虑使用构造函数创建结构体")
	
	// 构造函数示例
	NewPerson := func(name string, age int, city string) *Person {
		return &Person{
			Name: name,
			Age:  age,
			City: city,
		}
	}
	
	newPerson := NewPerson("构造函数创建", 25, "杭州")
	fmt.Printf("构造函数创建的人: %+v\n", *newPerson)
}

/*
运行命令: conda activate golang && go run 04_data_structures/04_structs.go

Go语言结构体总结：

1. 结构体特点：
   - 值类型，赋值时复制
   - 可以包含不同类型的字段
   - 支持嵌套和匿名字段
   - 可以定义方法

2. 定义语法：
   type StructName struct {
       Field1 Type1
       Field2 Type2
   }

3. 初始化方式：
   - 零值初始化：var s StructName
   - 字面量：StructName{field1, field2}
   - 指定字段：StructName{Field1: value1}
   - 指针：&StructName{...}

4. 字段访问：
   - 点号操作符：s.Field
   - 指针自动解引用：p.Field

5. 特殊结构体：
   - 匿名字段：类型名作为字段名
   - 空结构体：不占用内存
   - 嵌套结构体：结构体作为字段

6. 结构体标签：
   - 元数据标注
   - JSON序列化
   - 数据库映射

7. 内存特点：
   - 字段按声明顺序存储
   - 内存对齐优化
   - 大小受字段类型和对齐影响

8. 使用场景：
   - 数据建模
   - 配置管理
   - API响应结构
   - 数据库实体
*/
