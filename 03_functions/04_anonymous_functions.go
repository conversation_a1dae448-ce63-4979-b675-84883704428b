// 04_anonymous_functions.go - Go语言匿名函数和闭包
// 这个文件演示了Go语言中匿名函数、闭包和函数式编程的概念

package main

import (
	"fmt"
	"sort"
	"strings"
)

func main() {
	fmt.Println("=== Go语言匿名函数和闭包示例 ===")
	
	// 1. 基本匿名函数
	fmt.Println("\n--- 基本匿名函数 ---")
	
	// 定义并立即调用匿名函数
	result := func(a, b int) int {
		return a + b
	}(5, 3)
	fmt.Printf("匿名函数结果: %d\n", result) // 输出: 匿名函数结果: 8
	
	// 将匿名函数赋值给变量
	multiply := func(x, y int) int {
		return x * y
	}
	fmt.Printf("5 * 4 = %d\n", multiply(5, 4)) // 输出: 5 * 4 = 20
	
	// 2. 匿名函数作为参数
	fmt.Println("\n--- 匿名函数作为参数 ---")
	
	// 高阶函数：接受函数作为参数
	calculate := func(a, b int, operation func(int, int) int) int {
		return operation(a, b)
	}
	
	// 使用匿名函数作为参数
	sum := calculate(10, 5, func(x, y int) int {
		return x + y
	})
	
	difference := calculate(10, 5, func(x, y int) int {
		return x - y
	})
	
	fmt.Printf("10 + 5 = %d\n", sum)        // 输出: 10 + 5 = 15
	fmt.Printf("10 - 5 = %d\n", difference) // 输出: 10 - 5 = 5
	
	// 3. 闭包基础
	fmt.Println("\n--- 闭包基础 ---")
	
	// 闭包：函数可以访问其外部作用域的变量
	x := 10
	closure := func() int {
		return x * 2 // 访问外部变量x
	}
	
	fmt.Printf("闭包结果: %d\n", closure()) // 输出: 闭包结果: 20
	
	// 修改外部变量
	x = 20
	fmt.Printf("修改x后的闭包结果: %d\n", closure()) // 输出: 修改x后的闭包结果: 40
	
	// 4. 闭包捕获变量
	fmt.Println("\n--- 闭包捕获变量 ---")
	
	// 创建一个计数器闭包
	createCounter := func() func() int {
		count := 0
		return func() int {
			count++
			return count
		}
	}
	
	counter1 := createCounter()
	counter2 := createCounter()
	
	fmt.Printf("counter1: %d\n", counter1()) // 输出: counter1: 1
	fmt.Printf("counter1: %d\n", counter1()) // 输出: counter1: 2
	fmt.Printf("counter2: %d\n", counter2()) // 输出: counter2: 1
	fmt.Printf("counter1: %d\n", counter1()) // 输出: counter1: 3
	
	// 5. 闭包修改外部变量
	fmt.Println("\n--- 闭包修改外部变量 ---")
	
	balance := 100.0
	
	// 创建银行账户操作闭包
	deposit := func(amount float64) float64 {
		balance += amount
		return balance
	}
	
	withdraw := func(amount float64) float64 {
		if balance >= amount {
			balance -= amount
		}
		return balance
	}
	
	fmt.Printf("初始余额: %.2f\n", balance)           // 输出: 初始余额: 100.00
	fmt.Printf("存款50后: %.2f\n", deposit(50))      // 输出: 存款50后: 150.00
	fmt.Printf("取款30后: %.2f\n", withdraw(30))     // 输出: 取款30后: 120.00
	fmt.Printf("取款200后: %.2f\n", withdraw(200))   // 输出: 取款200后: 120.00 (余额不足)
	
	// 6. 函数工厂
	fmt.Println("\n--- 函数工厂 ---")
	
	// 创建乘法器工厂
	createMultiplier := func(factor int) func(int) int {
		return func(n int) int {
			return n * factor
		}
	}
	
	double := createMultiplier(2)
	triple := createMultiplier(3)
	
	fmt.Printf("double(5) = %d\n", double(5)) // 输出: double(5) = 10
	fmt.Printf("triple(5) = %d\n", triple(5)) // 输出: triple(5) = 15
	
	// 7. 延迟执行
	fmt.Println("\n--- 延迟执行 ---")
	
	// 创建延迟执行的函数
	createDelayedPrinter := func(message string) func() {
		return func() {
			fmt.Printf("延迟打印: %s\n", message)
		}
	}
	
	printer1 := createDelayedPrinter("Hello")
	printer2 := createDelayedPrinter("World")
	
	fmt.Println("创建了延迟打印函数")
	printer1() // 输出: 延迟打印: Hello
	printer2() // 输出: 延迟打印: World
	
	// 8. 函数式编程：map操作
	fmt.Println("\n--- 函数式编程：map操作 ---")
	
	// 实现map函数
	mapInts := func(slice []int, fn func(int) int) []int {
		result := make([]int, len(slice))
		for i, v := range slice {
			result[i] = fn(v)
		}
		return result
	}
	
	numbers := []int{1, 2, 3, 4, 5}
	
	// 使用匿名函数进行映射
	squared := mapInts(numbers, func(n int) int {
		return n * n
	})
	
	doubled := mapInts(numbers, func(n int) int {
		return n * 2
	})
	
	fmt.Printf("原数组: %v\n", numbers)  // 输出: 原数组: [1 2 3 4 5]
	fmt.Printf("平方: %v\n", squared)   // 输出: 平方: [1 4 9 16 25]
	fmt.Printf("翻倍: %v\n", doubled)   // 输出: 翻倍: [2 4 6 8 10]
	
	// 9. 函数式编程：filter操作
	fmt.Println("\n--- 函数式编程：filter操作 ---")
	
	// 实现filter函数
	filterInts := func(slice []int, predicate func(int) bool) []int {
		var result []int
		for _, v := range slice {
			if predicate(v) {
				result = append(result, v)
			}
		}
		return result
	}
	
	// 过滤偶数
	evens := filterInts(numbers, func(n int) bool {
		return n%2 == 0
	})
	
	// 过滤大于3的数
	greaterThan3 := filterInts(numbers, func(n int) bool {
		return n > 3
	})
	
	fmt.Printf("偶数: %v\n", evens)           // 输出: 偶数: [2 4]
	fmt.Printf("大于3的数: %v\n", greaterThan3) // 输出: 大于3的数: [4 5]
	
	// 10. 函数式编程：reduce操作
	fmt.Println("\n--- 函数式编程：reduce操作 ---")
	
	// 实现reduce函数
	reduceInts := func(slice []int, initial int, reducer func(int, int) int) int {
		result := initial
		for _, v := range slice {
			result = reducer(result, v)
		}
		return result
	}
	
	// 求和
	sum2 := reduceInts(numbers, 0, func(acc, n int) int {
		return acc + n
	})
	
	// 求积
	product := reduceInts(numbers, 1, func(acc, n int) int {
		return acc * n
	})
	
	fmt.Printf("数组求和: %d\n", sum2)    // 输出: 数组求和: 15
	fmt.Printf("数组求积: %d\n", product) // 输出: 数组求积: 120
	
	// 11. 实际应用：事件处理
	fmt.Println("\n--- 实际应用：事件处理 ---")
	
	// 事件处理器类型
	type EventHandler func(string)
	
	// 事件管理器
	eventHandlers := make(map[string][]EventHandler)
	
	// 注册事件处理器
	addEventListener := func(event string, handler EventHandler) {
		eventHandlers[event] = append(eventHandlers[event], handler)
	}
	
	// 触发事件
	triggerEvent := func(event string, data string) {
		if handlers, exists := eventHandlers[event]; exists {
			for _, handler := range handlers {
				handler(data)
			}
		}
	}
	
	// 注册事件处理器
	addEventListener("user_login", func(data string) {
		fmt.Printf("用户登录处理器1: %s\n", data)
	})
	
	addEventListener("user_login", func(data string) {
		fmt.Printf("用户登录处理器2: %s\n", data)
	})
	
	// 触发事件
	triggerEvent("user_login", "用户张三登录")
	// 输出: 用户登录处理器1: 用户张三登录
	//      用户登录处理器2: 用户张三登录
	
	// 12. 实际应用：自定义排序
	fmt.Println("\n--- 实际应用：自定义排序 ---")
	
	type Person struct {
		Name string
		Age  int
	}
	
	people := []Person{
		{"张三", 25},
		{"李四", 30},
		{"王五", 20},
		{"赵六", 35},
	}
	
	fmt.Printf("排序前: %+v\n", people)
	
	// 按年龄排序
	sort.Slice(people, func(i, j int) bool {
		return people[i].Age < people[j].Age
	})
	
	fmt.Printf("按年龄排序: %+v\n", people)
	
	// 按姓名排序
	sort.Slice(people, func(i, j int) bool {
		return people[i].Name < people[j].Name
	})
	
	fmt.Printf("按姓名排序: %+v\n", people)
	
	// 13. 实际应用：配置验证
	fmt.Println("\n--- 实际应用：配置验证 ---")
	
	type Config struct {
		Host string
		Port int
		SSL  bool
	}
	
	// 验证器类型
	type Validator func(Config) error
	
	// 创建验证器
	createHostValidator := func() Validator {
		return func(config Config) error {
			if config.Host == "" {
				return fmt.Errorf("host不能为空")
			}
			return nil
		}
	}
	
	createPortValidator := func(min, max int) Validator {
		return func(config Config) error {
			if config.Port < min || config.Port > max {
				return fmt.Errorf("端口必须在%d-%d之间", min, max)
			}
			return nil
		}
	}
	
	// 验证配置
	validateConfig := func(config Config, validators ...Validator) []error {
		var errors []error
		for _, validator := range validators {
			if err := validator(config); err != nil {
				errors = append(errors, err)
			}
		}
		return errors
	}
	
	// 测试配置验证
	config1 := Config{Host: "localhost", Port: 8080, SSL: true}
	config2 := Config{Host: "", Port: 99999, SSL: false}
	
	validators := []Validator{
		createHostValidator(),
		createPortValidator(1000, 65535),
	}
	
	errors1 := validateConfig(config1, validators...)
	errors2 := validateConfig(config2, validators...)
	
	fmt.Printf("配置1验证结果: %v\n", errors1) // 输出: 配置1验证结果: []
	fmt.Printf("配置2验证结果: %v\n", errors2) // 输出: 配置2验证结果: [host不能为空 端口必须在1000-65535之间]
	
	// 14. 实际应用：字符串处理管道
	fmt.Println("\n--- 实际应用：字符串处理管道 ---")
	
	// 字符串处理函数类型
	type StringProcessor func(string) string
	
	// 创建处理管道
	createPipeline := func(processors ...StringProcessor) StringProcessor {
		return func(input string) string {
			result := input
			for _, processor := range processors {
				result = processor(result)
			}
			return result
		}
	}
	
	// 创建各种处理器
	toUpper := func(s string) string {
		return strings.ToUpper(s)
	}
	
	addPrefix := func(prefix string) StringProcessor {
		return func(s string) string {
			return prefix + s
		}
	}
	
	addSuffix := func(suffix string) StringProcessor {
		return func(s string) string {
			return s + suffix
		}
	}
	
	// 创建处理管道
	pipeline := createPipeline(
		toUpper,
		addPrefix(">>> "),
		addSuffix(" <<<"),
	)
	
	input := "hello world"
	output := pipeline(input)
	fmt.Printf("输入: %s\n", input)   // 输出: 输入: hello world
	fmt.Printf("输出: %s\n", output)  // 输出: 输出: >>> HELLO WORLD <<<
}

/*
运行命令: go run 03_functions/04_anonymous_functions.go

Go语言匿名函数和闭包总结：

1. 匿名函数：
   - 没有名称的函数
   - 可以立即调用或赋值给变量
   - 语法：func(参数) 返回值 { 函数体 }

2. 闭包：
   - 函数可以访问其外部作用域的变量
   - 即使外部函数已经返回，闭包仍然可以访问这些变量
   - 每个闭包都有自己的变量副本

3. 函数作为一等公民：
   - 可以作为参数传递
   - 可以作为返回值
   - 可以赋值给变量
   - 可以存储在数据结构中

4. 常见应用场景：
   - 事件处理
   - 回调函数
   - 函数式编程（map、filter、reduce）
   - 工厂模式
   - 配置和验证
   - 中间件模式

5. 最佳实践：
   - 合理使用闭包，避免内存泄漏
   - 函数式编程提高代码的可读性和可维护性
   - 使用匿名函数简化代码
   - 注意闭包中变量的生命周期

6. 注意事项：
   - 闭包会持有外部变量的引用，可能导致内存泄漏
   - 在循环中创建闭包时要注意变量捕获问题
   - 过度使用可能影响代码可读性
*/
