// 01_basic_functions.go - Go语言基本函数
// 这个文件演示了Go语言中函数的基本定义和使用

package main

import "fmt"

// 1. 无参数无返回值的函数
func sayHello() {
	fmt.Println("Hello, World!")
}

// 2. 有参数无返回值的函数
func greet(name string) {
	fmt.Printf("Hello, %s!\n", name)
}

// 3. 有参数有返回值的函数
func add(a, b int) int {
	return a + b
}

// 4. 多个参数不同类型
func introduce(name string, age int, height float64) {
	fmt.Printf("我叫%s，今年%d岁，身高%.1f厘米\n", name, age, height)
}

// 5. 多个返回值
func divide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}
	return a / b, nil
}

// 6. 命名返回值
func calculate(a, b int) (sum, product int) {
	sum = a + b
	product = a * b
	return // 裸返回，自动返回命名的返回值
}

// 7. 可变参数函数
func sum(numbers ...int) int {
	total := 0
	for _, num := range numbers {
		total += num
	}
	return total
}

// 8. 混合参数（固定参数 + 可变参数）
func printInfo(prefix string, values ...interface{}) {
	fmt.Print(prefix + ": ")
	for i, value := range values {
		if i > 0 {
			fmt.Print(", ")
		}
		fmt.Print(value)
	}
	fmt.Println()
}

// 9. 递归函数
func factorial(n int) int {
	if n <= 1 {
		return 1
	}
	return n * factorial(n-1)
}

// 10. 函数作为参数
func operate(a, b int, op func(int, int) int) int {
	return op(a, b)
}

// 用于测试的操作函数
func multiply(x, y int) int {
	return x * y
}

func subtract(x, y int) int {
	return x - y
}

func main() {
	fmt.Println("=== Go语言基本函数示例 ===")
	
	// 1. 调用无参数无返回值的函数
	fmt.Println("\n--- 无参数无返回值函数 ---")
	sayHello() // 输出: Hello, World!
	
	// 2. 调用有参数无返回值的函数
	fmt.Println("\n--- 有参数无返回值函数 ---")
	greet("张三") // 输出: Hello, 张三!
	greet("李四") // 输出: Hello, 李四!
	
	// 3. 调用有参数有返回值的函数
	fmt.Println("\n--- 有参数有返回值函数 ---")
	result := add(5, 3)
	fmt.Printf("5 + 3 = %d\n", result) // 输出: 5 + 3 = 8
	
	// 直接在表达式中使用函数返回值
	fmt.Printf("10 + 20 = %d\n", add(10, 20)) // 输出: 10 + 20 = 30
	
	// 4. 调用多参数函数
	fmt.Println("\n--- 多参数函数 ---")
	introduce("王五", 25, 175.5) // 输出: 我叫王五，今年25岁，身高175.5厘米
	
	// 5. 调用多返回值函数
	fmt.Println("\n--- 多返回值函数 ---")
	quotient, err := divide(10, 3)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
	} else {
		fmt.Printf("10 / 3 = %.3f\n", quotient) // 输出: 10 / 3 = 3.333
	}
	
	// 处理除零错误
	_, err = divide(10, 0)
	if err != nil {
		fmt.Printf("错误: %v\n", err) // 输出: 错误: 除数不能为零
	}
	
	// 6. 调用命名返回值函数
	fmt.Println("\n--- 命名返回值函数 ---")
	s, p := calculate(4, 5)
	fmt.Printf("4和5的和: %d, 积: %d\n", s, p) // 输出: 4和5的和: 9, 积: 20
	
	// 7. 调用可变参数函数
	fmt.Println("\n--- 可变参数函数 ---")
	fmt.Printf("sum() = %d\n", sum())                    // 输出: sum() = 0
	fmt.Printf("sum(1) = %d\n", sum(1))                  // 输出: sum(1) = 1
	fmt.Printf("sum(1, 2, 3) = %d\n", sum(1, 2, 3))     // 输出: sum(1, 2, 3) = 6
	fmt.Printf("sum(1, 2, 3, 4, 5) = %d\n", sum(1, 2, 3, 4, 5)) // 输出: sum(1, 2, 3, 4, 5) = 15
	
	// 使用切片作为可变参数
	numbers := []int{10, 20, 30, 40}
	fmt.Printf("sum(10, 20, 30, 40) = %d\n", sum(numbers...)) // 输出: sum(10, 20, 30, 40) = 100
	
	// 8. 调用混合参数函数
	fmt.Println("\n--- 混合参数函数 ---")
	printInfo("数字", 1, 2, 3, 4, 5)                    // 输出: 数字: 1, 2, 3, 4, 5
	printInfo("信息", "姓名", "年龄", true, 3.14)        // 输出: 信息: 姓名, 年龄, true, 3.14
	
	// 9. 调用递归函数
	fmt.Println("\n--- 递归函数 ---")
	for i := 0; i <= 5; i++ {
		fmt.Printf("%d! = %d\n", i, factorial(i))
	}
	// 输出:
	// 0! = 1
	// 1! = 1
	// 2! = 2
	// 3! = 6
	// 4! = 24
	// 5! = 120
	
	// 10. 函数作为参数
	fmt.Println("\n--- 函数作为参数 ---")
	a, b := 8, 3
	
	// 使用不同的操作函数
	fmt.Printf("%d + %d = %d\n", a, b, operate(a, b, add))      // 输出: 8 + 3 = 11
	fmt.Printf("%d * %d = %d\n", a, b, operate(a, b, multiply)) // 输出: 8 * 3 = 24
	fmt.Printf("%d - %d = %d\n", a, b, operate(a, b, subtract)) // 输出: 8 - 3 = 5
	
	// 11. 函数变量
	fmt.Println("\n--- 函数变量 ---")
	var mathOp func(int, int) int
	
	mathOp = add
	fmt.Printf("使用add函数: 5 + 7 = %d\n", mathOp(5, 7)) // 输出: 使用add函数: 5 + 7 = 12
	
	mathOp = multiply
	fmt.Printf("使用multiply函数: 5 * 7 = %d\n", mathOp(5, 7)) // 输出: 使用multiply函数: 5 * 7 = 35
	
	// 12. 实际应用示例：数学运算库
	fmt.Println("\n--- 实际应用：数学运算 ---")
	fmt.Printf("圆的面积 (r=5): %.2f\n", circleArea(5))           // 输出: 圆的面积 (r=5): 78.54
	fmt.Printf("矩形面积 (5x3): %.2f\n", rectangleArea(5, 3))     // 输出: 矩形面积 (5x3): 15.00
	fmt.Printf("是否为偶数 (8): %t\n", isEven(8))                 // 输出: 是否为偶数 (8): true
	fmt.Printf("是否为偶数 (7): %t\n", isEven(7))                 // 输出: 是否为偶数 (7): false
	
	// 13. 实际应用示例：字符串处理
	fmt.Println("\n--- 实际应用：字符串处理 ---")
	text := "Hello World"
	fmt.Printf("字符串长度: %d\n", stringLength(text))            // 输出: 字符串长度: 11
	fmt.Printf("反转字符串: %s\n", reverseString(text))           // 输出: 反转字符串: dlroW olleH
	fmt.Printf("是否回文 (level): %t\n", isPalindrome("level"))   // 输出: 是否回文 (level): true
	fmt.Printf("是否回文 (hello): %t\n", isPalindrome("hello"))   // 输出: 是否回文 (hello): false
}

// 实际应用函数示例

// 计算圆的面积
func circleArea(radius float64) float64 {
	const pi = 3.14159
	return pi * radius * radius
}

// 计算矩形面积
func rectangleArea(width, height float64) float64 {
	return width * height
}

// 判断是否为偶数
func isEven(n int) bool {
	return n%2 == 0
}

// 获取字符串长度
func stringLength(s string) int {
	return len(s)
}

// 反转字符串
func reverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// 判断是否为回文
func isPalindrome(s string) bool {
	return s == reverseString(s)
}

/*
运行命令: go run 03_functions/01_basic_functions.go

Go语言函数特点：

1. 函数定义语法：func name(parameters) returnType { }
2. 支持多返回值
3. 支持命名返回值
4. 支持可变参数 (...type)
5. 函数是一等公民，可以作为变量、参数、返回值
6. 支持递归
7. 参数传递是值传递（除了slice、map、channel等引用类型）

函数命名规范：
- 首字母大写：公开函数（可被其他包访问）
- 首字母小写：私有函数（仅包内访问）
- 使用驼峰命名法

最佳实践：
1. 函数应该短小精悍，职责单一
2. 合理使用多返回值处理错误
3. 使用命名返回值提高代码可读性
4. 避免过深的递归调用
5. 函数名应该清晰表达其功能
*/
