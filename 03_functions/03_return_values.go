// 03_return_values.go - Go语言函数返回值
// 这个文件演示了Go语言中函数返回值的各种用法

package main

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
)

// 1. 单个返回值
func square(x int) int {
	return x * x
}

// 2. 多个返回值
func divmod(a, b int) (int, int) {
	return a / b, a % b
}

// 3. 命名返回值
func rectangleInfo(width, height float64) (area, perimeter float64) {
	area = width * height
	perimeter = 2 * (width + height)
	return // 裸返回，自动返回命名的返回值
}

// 4. 错误处理模式
func safeDivide(a, b float64) (float64, error) {
	if b == 0 {
		return 0, errors.New("division by zero")
	}
	return a / b, nil
}

// 5. 多种类型的返回值
func parseUserInfo(input string) (string, int, bool, error) {
	parts := strings.Split(input, ",")
	if len(parts) != 3 {
		return "", 0, false, errors.New("invalid format")
	}
	
	name := strings.TrimSpace(parts[0])
	age, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil {
		return "", 0, false, errors.New("invalid age")
	}
	
	active := strings.TrimSpace(parts[2]) == "true"
	
	return name, age, active, nil
}

// 6. 返回函数
func getOperation(op string) func(int, int) int {
	switch op {
	case "+":
		return func(a, b int) int { return a + b }
	case "-":
		return func(a, b int) int { return a - b }
	case "*":
		return func(a, b int) int { return a * b }
	case "/":
		return func(a, b int) int { 
			if b != 0 {
				return a / b
			}
			return 0
		}
	default:
		return func(a, b int) int { return 0 }
	}
}

// 7. 返回接口
func createShape(shapeType string) interface{} {
	switch shapeType {
	case "circle":
		return Circle{Radius: 5}
	case "rectangle":
		return Rectangle{Width: 4, Height: 6}
	default:
		return nil
	}
}

type Circle struct {
	Radius float64
}

type Rectangle struct {
	Width, Height float64
}

// 8. 返回指针
func createPerson(name string, age int) *Person {
	return &Person{
		Name: name,
		Age:  age,
	}
}

type Person struct {
	Name string
	Age  int
}

// 9. 返回切片
func generateNumbers(start, end int) []int {
	if start > end {
		return nil
	}
	
	numbers := make([]int, 0, end-start+1)
	for i := start; i <= end; i++ {
		numbers = append(numbers, i)
	}
	return numbers
}

// 10. 返回映射
func wordCount(text string) map[string]int {
	words := strings.Fields(strings.ToLower(text))
	count := make(map[string]int)
	
	for _, word := range words {
		count[word]++
	}
	
	return count
}

// 11. 返回通道
func numberGenerator(max int) <-chan int {
	ch := make(chan int)
	
	go func() {
		defer close(ch)
		for i := 1; i <= max; i++ {
			ch <- i
		}
	}()
	
	return ch
}

// 12. 复杂返回值示例：数据库查询模拟
type User struct {
	ID    int
	Name  string
	Email string
}

func findUser(id int) (*User, bool, error) {
	// 模拟数据库
	users := map[int]User{
		1: {ID: 1, Name: "张三", Email: "<EMAIL>"},
		2: {ID: 2, Name: "李四", Email: "<EMAIL>"},
	}
	
	if id <= 0 {
		return nil, false, errors.New("invalid user ID")
	}
	
	user, exists := users[id]
	if !exists {
		return nil, false, nil // 用户不存在，但不是错误
	}
	
	return &user, true, nil
}

func main() {
	fmt.Println("=== Go语言函数返回值示例 ===")
	
	// 1. 单个返回值
	fmt.Println("\n--- 单个返回值 ---")
	result := square(5)
	fmt.Printf("5的平方 = %d\n", result) // 输出: 5的平方 = 25
	
	// 2. 多个返回值
	fmt.Println("\n--- 多个返回值 ---")
	quotient, remainder := divmod(17, 5)
	fmt.Printf("17 ÷ 5 = %d 余 %d\n", quotient, remainder) // 输出: 17 ÷ 5 = 3 余 2
	
	// 忽略某个返回值
	q, _ := divmod(20, 3)
	fmt.Printf("20 ÷ 3 的商 = %d\n", q) // 输出: 20 ÷ 3 的商 = 6
	
	// 3. 命名返回值
	fmt.Println("\n--- 命名返回值 ---")
	area, perimeter := rectangleInfo(4, 5)
	fmt.Printf("矩形面积 = %.2f, 周长 = %.2f\n", area, perimeter) // 输出: 矩形面积 = 20.00, 周长 = 18.00
	
	// 4. 错误处理模式
	fmt.Println("\n--- 错误处理模式 ---")
	
	// 正常情况
	result1, err1 := safeDivide(10, 2)
	if err1 != nil {
		fmt.Printf("错误: %v\n", err1)
	} else {
		fmt.Printf("10 ÷ 2 = %.2f\n", result1) // 输出: 10 ÷ 2 = 5.00
	}
	
	// 错误情况
	result2, err2 := safeDivide(10, 0)
	if err2 != nil {
		fmt.Printf("错误: %v\n", err2) // 输出: 错误: division by zero
	} else {
		fmt.Printf("结果: %.2f\n", result2)
	}
	
	// 5. 多种类型的返回值
	fmt.Println("\n--- 多种类型的返回值 ---")
	
	// 正确格式
	name1, age1, active1, err3 := parseUserInfo("张三, 25, true")
	if err3 != nil {
		fmt.Printf("解析错误: %v\n", err3)
	} else {
		fmt.Printf("用户: %s, 年龄: %d, 活跃: %t\n", name1, age1, active1) // 输出: 用户: 张三, 年龄: 25, 活跃: true
	}
	
	// 错误格式
	_, _, _, err4 := parseUserInfo("invalid format")
	if err4 != nil {
		fmt.Printf("解析错误: %v\n", err4) // 输出: 解析错误: invalid format
	}
	
	// 6. 返回函数
	fmt.Println("\n--- 返回函数 ---")
	addFunc := getOperation("+")
	multiplyFunc := getOperation("*")
	
	fmt.Printf("5 + 3 = %d\n", addFunc(5, 3))      // 输出: 5 + 3 = 8
	fmt.Printf("5 * 3 = %d\n", multiplyFunc(5, 3)) // 输出: 5 * 3 = 15
	
	// 7. 返回接口
	fmt.Println("\n--- 返回接口 ---")
	circle := createShape("circle")
	rectangle := createShape("rectangle")
	
	fmt.Printf("圆形: %+v\n", circle)     // 输出: 圆形: {Radius:5}
	fmt.Printf("矩形: %+v\n", rectangle)  // 输出: 矩形: {Width:4 Height:6}
	
	// 类型断言
	if c, ok := circle.(Circle); ok {
		fmt.Printf("圆的半径: %.1f\n", c.Radius) // 输出: 圆的半径: 5.0
	}
	
	// 8. 返回指针
	fmt.Println("\n--- 返回指针 ---")
	person := createPerson("王五", 30)
	fmt.Printf("创建的人员: %+v\n", *person) // 输出: 创建的人员: {Name:王五 Age:30}
	
	// 9. 返回切片
	fmt.Println("\n--- 返回切片 ---")
	numbers := generateNumbers(1, 5)
	fmt.Printf("生成的数字: %v\n", numbers) // 输出: 生成的数字: [1 2 3 4 5]
	
	emptyNumbers := generateNumbers(5, 1)
	fmt.Printf("空切片: %v\n", emptyNumbers) // 输出: 空切片: []
	
	// 10. 返回映射
	fmt.Println("\n--- 返回映射 ---")
	text := "hello world hello go world"
	counts := wordCount(text)
	fmt.Printf("单词计数: %v\n", counts) // 输出: 单词计数: map[go:1 hello:2 world:2]
	
	// 11. 返回通道
	fmt.Println("\n--- 返回通道 ---")
	ch := numberGenerator(5)
	fmt.Print("从通道接收: ")
	for num := range ch {
		fmt.Printf("%d ", num) // 输出: 从通道接收: 1 2 3 4 5 
	}
	fmt.Println()
	
	// 12. 复杂返回值示例
	fmt.Println("\n--- 复杂返回值示例 ---")
	
	// 查找存在的用户
	user1, found1, err5 := findUser(1)
	if err5 != nil {
		fmt.Printf("查询错误: %v\n", err5)
	} else if found1 {
		fmt.Printf("找到用户: %+v\n", *user1) // 输出: 找到用户: {ID:1 Name:张三 Email:<EMAIL>}
	} else {
		fmt.Println("用户不存在")
	}
	
	// 查找不存在的用户
	user2, found2, err6 := findUser(999)
	if err6 != nil {
		fmt.Printf("查询错误: %v\n", err6)
	} else if found2 {
		fmt.Printf("找到用户: %+v\n", *user2)
	} else {
		fmt.Println("用户不存在") // 输出: 用户不存在
	}
	
	// 无效ID
	_, _, err7 := findUser(-1)
	if err7 != nil {
		fmt.Printf("查询错误: %v\n", err7) // 输出: 查询错误: invalid user ID
	}
	
	// 13. 实际应用：文件处理函数
	fmt.Println("\n--- 实际应用：文件处理模拟 ---")
	
	processFile := func(filename string) ([]string, int, error) {
		// 模拟文件处理
		if filename == "" {
			return nil, 0, errors.New("filename cannot be empty")
		}
		
		if !strings.HasSuffix(filename, ".txt") {
			return nil, 0, errors.New("only .txt files are supported")
		}
		
		// 模拟读取文件内容
		lines := []string{
			"第一行内容",
			"第二行内容",
			"第三行内容",
		}
		
		return lines, len(lines), nil
	}
	
	lines, count, err := processFile("test.txt")
	if err != nil {
		fmt.Printf("文件处理错误: %v\n", err)
	} else {
		fmt.Printf("处理了%d行内容: %v\n", count, lines) // 输出: 处理了3行内容: [第一行内容 第二行内容 第三行内容]
	}
	
	// 14. 实际应用：数据转换函数
	fmt.Println("\n--- 实际应用：数据转换 ---")
	
	convertData := func(data interface{}) (string, bool) {
		switch v := data.(type) {
		case string:
			return v, true
		case int:
			return strconv.Itoa(v), true
		case float64:
			return fmt.Sprintf("%.2f", v), true
		case bool:
			return strconv.FormatBool(v), true
		default:
			return "", false
		}
	}
	
	// 测试不同类型的转换
	testData := []interface{}{42, 3.14, true, "hello", []int{1, 2, 3}}
	
	for _, data := range testData {
		if str, ok := convertData(data); ok {
			fmt.Printf("%v -> %s\n", data, str)
		} else {
			fmt.Printf("%v -> 转换失败\n", data) // 输出: [1 2 3] -> 转换失败
		}
	}
}

/*
运行命令: go run 03_functions/03_return_values.go

Go语言返回值特点：

1. 支持多返回值：
   - 可以返回任意数量的值
   - 常用于返回结果和错误

2. 命名返回值：
   - 在函数签名中命名返回值
   - 可以使用裸返回（return）
   - 提高代码可读性

3. 错误处理模式：
   - 通常最后一个返回值是error类型
   - nil表示没有错误
   - 遵循"早返回"原则

4. 返回值类型：
   - 基本类型
   - 复合类型（切片、映射、结构体等）
   - 指针类型
   - 接口类型
   - 函数类型
   - 通道类型

5. 最佳实践：
   - 使用多返回值处理错误
   - 合理使用命名返回值
   - 保持返回值数量适中（通常不超过3-4个）
   - 使用_忽略不需要的返回值
   - 错误处理要及时和明确

6. 常见模式：
   - (result, error) - 结果和错误
   - (value, ok) - 值和存在性检查
   - (pointer, found, error) - 指针、是否找到、错误
*/
