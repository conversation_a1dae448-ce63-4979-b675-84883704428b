// 02_parameters.go - Go语言函数参数传递
// 这个文件演示了Go语言中函数参数传递的各种方式和特点

package main

import "fmt"

// 1. 值传递示例
func modifyValue(x int) {
	fmt.Printf("函数内修改前: x = %d\n", x)
	x = 100
	fmt.Printf("函数内修改后: x = %d\n", x)
}

// 2. 指针传递示例
func modifyPointer(x *int) {
	fmt.Printf("函数内修改前: *x = %d\n", *x)
	*x = 200
	fmt.Printf("函数内修改后: *x = %d\n", *x)
}

// 3. 切片参数（引用类型）
func modifySlice(s []int) {
	fmt.Printf("函数内修改前: %v\n", s)
	if len(s) > 0 {
		s[0] = 999 // 修改切片元素
	}
	s = append(s, 100) // 追加元素
	fmt.Printf("函数内修改后: %v\n", s)
}

// 4. 映射参数（引用类型）
func modifyMap(m map[string]int) {
	fmt.Printf("函数内修改前: %v\n", m)
	m["new"] = 100 // 添加新键值对
	m["age"] = 30  // 修改现有值
	fmt.Printf("函数内修改后: %v\n", m)
}

// 5. 结构体值传递
type Person struct {
	Name string
	Age  int
}

func modifyPersonValue(p Person) {
	fmt.Printf("函数内修改前: %+v\n", p)
	p.Name = "修改后的名字"
	p.Age = 100
	fmt.Printf("函数内修改后: %+v\n", p)
}

// 6. 结构体指针传递
func modifyPersonPointer(p *Person) {
	fmt.Printf("函数内修改前: %+v\n", *p)
	p.Name = "指针修改的名字"
	p.Age = 200
	fmt.Printf("函数内修改后: %+v\n", *p)
}

// 7. 可变参数的不同用法
func printNumbers(prefix string, numbers ...int) {
	fmt.Printf("%s: ", prefix)
	for i, num := range numbers {
		if i > 0 {
			fmt.Print(", ")
		}
		fmt.Print(num)
	}
	fmt.Println()
}

// 8. 接口参数
func printAnything(items ...interface{}) {
	for i, item := range items {
		if i > 0 {
			fmt.Print(", ")
		}
		fmt.Printf("%v", item)
	}
	fmt.Println()
}

// 9. 函数参数
func applyOperation(a, b int, op func(int, int) int) int {
	return op(a, b)
}

// 10. 通道参数
func sendData(ch chan<- int, data int) {
	ch <- data
}

func receiveData(ch <-chan int) int {
	return <-ch
}

func main() {
	fmt.Println("=== Go语言函数参数传递示例 ===")
	
	// 1. 值传递测试
	fmt.Println("\n--- 值传递 ---")
	num := 50
	fmt.Printf("调用前: num = %d\n", num)
	modifyValue(num)
	fmt.Printf("调用后: num = %d\n", num) // 输出: 调用后: num = 50 (值没有改变)
	
	// 2. 指针传递测试
	fmt.Println("\n--- 指针传递 ---")
	num2 := 50
	fmt.Printf("调用前: num2 = %d\n", num2)
	modifyPointer(&num2)
	fmt.Printf("调用后: num2 = %d\n", num2) // 输出: 调用后: num2 = 200 (值被修改)
	
	// 3. 切片参数测试
	fmt.Println("\n--- 切片参数 ---")
	slice := []int{1, 2, 3}
	fmt.Printf("调用前: %v\n", slice)
	modifySlice(slice)
	fmt.Printf("调用后: %v\n", slice) // 输出: [999, 2, 3] (第一个元素被修改，但长度没变)
	
	// 4. 映射参数测试
	fmt.Println("\n--- 映射参数 ---")
	person := map[string]int{"age": 25, "score": 85}
	fmt.Printf("调用前: %v\n", person)
	modifyMap(person)
	fmt.Printf("调用后: %v\n", person) // 输出: map[age:30 new:100 score:85] (映射被修改)
	
	// 5. 结构体值传递测试
	fmt.Println("\n--- 结构体值传递 ---")
	p1 := Person{Name: "张三", Age: 25}
	fmt.Printf("调用前: %+v\n", p1)
	modifyPersonValue(p1)
	fmt.Printf("调用后: %+v\n", p1) // 输出: {Name:张三 Age:25} (结构体没有改变)
	
	// 6. 结构体指针传递测试
	fmt.Println("\n--- 结构体指针传递 ---")
	p2 := Person{Name: "李四", Age: 30}
	fmt.Printf("调用前: %+v\n", p2)
	modifyPersonPointer(&p2)
	fmt.Printf("调用后: %+v\n", p2) // 输出: {Name:指针修改的名字 Age:200} (结构体被修改)
	
	// 7. 可变参数测试
	fmt.Println("\n--- 可变参数 ---")
	printNumbers("数字列表", 1, 2, 3, 4, 5) // 输出: 数字列表: 1, 2, 3, 4, 5
	
	// 使用切片作为可变参数
	nums := []int{10, 20, 30}
	printNumbers("切片数字", nums...) // 输出: 切片数字: 10, 20, 30
	
	// 8. 接口参数测试
	fmt.Println("\n--- 接口参数 ---")
	printAnything("字符串", 42, true, 3.14, Person{Name: "王五", Age: 35})
	// 输出: 字符串, 42, true, 3.14, {王五 35}
	
	// 9. 函数参数测试
	fmt.Println("\n--- 函数参数 ---")
	add := func(a, b int) int { return a + b }
	multiply := func(a, b int) int { return a * b }
	
	result1 := applyOperation(5, 3, add)
	result2 := applyOperation(5, 3, multiply)
	fmt.Printf("5 + 3 = %d\n", result1) // 输出: 5 + 3 = 8
	fmt.Printf("5 * 3 = %d\n", result2) // 输出: 5 * 3 = 15
	
	// 10. 通道参数测试
	fmt.Println("\n--- 通道参数 ---")
	ch := make(chan int, 1)
	
	sendData(ch, 42)
	received := receiveData(ch)
	fmt.Printf("通过通道接收到: %d\n", received) // 输出: 通过通道接收到: 42
	
	// 11. 参数传递性能比较示例
	fmt.Println("\n--- 参数传递性能考虑 ---")
	
	// 大结构体
	type LargeStruct struct {
		Data [1000]int
		Name string
		Info map[string]string
	}
	
	large := LargeStruct{
		Name: "大结构体",
		Info: map[string]string{"key": "value"},
	}
	
	// 值传递（复制整个结构体，性能较差）
	processLargeStructByValue := func(ls LargeStruct) {
		fmt.Printf("处理大结构体（值传递）: %s\n", ls.Name)
	}
	
	// 指针传递（只传递地址，性能较好）
	processLargeStructByPointer := func(ls *LargeStruct) {
		fmt.Printf("处理大结构体（指针传递）: %s\n", ls.Name)
	}
	
	processLargeStructByValue(large)    // 输出: 处理大结构体（值传递）: 大结构体
	processLargeStructByPointer(&large) // 输出: 处理大结构体（指针传递）: 大结构体
	
	// 12. 实际应用：数据验证函数
	fmt.Println("\n--- 实际应用：数据验证 ---")
	
	validateUser := func(name string, age int, email string) []string {
		var errors []string
		
		if name == "" {
			errors = append(errors, "姓名不能为空")
		}
		if age < 0 || age > 150 {
			errors = append(errors, "年龄必须在0-150之间")
		}
		if email == "" {
			errors = append(errors, "邮箱不能为空")
		}
		
		return errors
	}
	
	// 测试验证函数
	errors1 := validateUser("张三", 25, "<EMAIL>")
	errors2 := validateUser("", -5, "")
	
	fmt.Printf("用户1验证结果: %v\n", errors1) // 输出: 用户1验证结果: []
	fmt.Printf("用户2验证结果: %v\n", errors2) // 输出: 用户2验证结果: [姓名不能为空 年龄必须在0-150之间 邮箱不能为空]
	
	// 13. 实际应用：配置函数
	fmt.Println("\n--- 实际应用：配置函数 ---")
	
	type Config struct {
		Host string
		Port int
		SSL  bool
	}
	
	// 使用函数选项模式
	type ConfigOption func(*Config)
	
	WithHost := func(host string) ConfigOption {
		return func(c *Config) {
			c.Host = host
		}
	}
	
	WithPort := func(port int) ConfigOption {
		return func(c *Config) {
			c.Port = port
		}
	}
	
	WithSSL := func(ssl bool) ConfigOption {
		return func(c *Config) {
			c.SSL = ssl
		}
	}
	
	NewConfig := func(options ...ConfigOption) *Config {
		config := &Config{
			Host: "localhost",
			Port: 8080,
			SSL:  false,
		}
		
		for _, option := range options {
			option(config)
		}
		
		return config
	}
	
	// 使用配置函数
	config := NewConfig(
		WithHost("example.com"),
		WithPort(443),
		WithSSL(true),
	)
	
	fmt.Printf("配置: %+v\n", config) // 输出: 配置: &{Host:example.com Port:443 SSL:true}
}

/*
运行命令: go run 03_functions/02_parameters.go

Go语言参数传递总结：

1. 值传递：
   - 基本类型（int, float, bool, string等）
   - 数组
   - 结构体
   - 传递的是副本，函数内修改不影响原值

2. 引用传递：
   - 切片（slice）
   - 映射（map）
   - 通道（channel）
   - 接口（interface）
   - 函数类型
   - 传递的是引用，函数内修改会影响原值

3. 指针传递：
   - 显式传递指针（&variable）
   - 可以修改原值
   - 适用于大结构体，避免复制开销

4. 特殊情况：
   - 切片的底层数组可以被修改，但切片本身（长度、容量）的修改不会影响原切片
   - 映射的键值对可以被修改
   - 通道可以发送和接收数据

5. 性能考虑：
   - 大结构体使用指针传递
   - 小数据使用值传递
   - 切片、映射本身就是引用类型，无需使用指针

6. 最佳实践：
   - 需要修改原值时使用指针
   - 大结构体使用指针避免复制
   - 使用接口参数提高函数的通用性
   - 合理使用可变参数简化函数调用
*/
